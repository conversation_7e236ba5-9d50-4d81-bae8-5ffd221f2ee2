require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/user');
const bcrypt = require('bcryptjs');

async function createTestUser() {
  await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
  
  try {
    // Xóa user cũ nếu có
    await User.deleteOne({ email: '<EMAIL>' });
    
    // Tạo user mới với password đơn giản
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    const newUser = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword,
      phoneNumber: '0123456789',
      status: 'ACTIVE',
      role: 'CUSTOMER',
      isVerified: true,
      isLocked: false,
      address: '123 Test Street',
      birthDate: new Date('1990-01-01'),
      gender: 'MALE'
    });
    
    await newUser.save();
    console.log('✅ Test user created successfully');
    console.log('Email: <EMAIL>');
    console.log('Password: 123456');
    
    // Test login
    const testUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
    const isMatch = await bcrypt.compare('123456', testUser.password);
    console.log('Password test:', isMatch ? '✅ Match' : '❌ No match');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  
  await mongoose.disconnect();
}

createTestUser();
