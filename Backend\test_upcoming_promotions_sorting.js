require('dotenv').config();
const axios = require('axios');

// Test upcoming promotions sorting in My Promotions
async function testUpcomingPromotionsSorting() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  // Helper function to get promotion status (mimic frontend logic)
  const getPromotionStatus = (promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);
    
    if (now < startDate) {
      return { status: 'upcoming', message: 'Coming Soon' };
    } else if (now > endDate) {
      return { status: 'expired', message: 'Expired' };
    } else if (promotion.userCanUse === false) {
      return { status: 'used up', message: 'Used Up' };
    } else {
      return { status: 'active', message: 'Active' };
    }
  };
  
  try {
    console.log("🎯 Testing Upcoming Promotions Sorting in My Promotions");
    console.log("=" .repeat(70));
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      const allPromotions = response.data.promotions;
      
      // Apply My Promotions filter logic
      const myPromotions = allPromotions.filter(promotion => {
        if (promotion.type === 'PUBLIC') {
          return true;
        }
        return promotion.isClaimed === true;
      });
      
      console.log(`📊 Total promotions: ${allPromotions.length}`);
      console.log(`📊 My Promotions: ${myPromotions.length}`);
      
      // Analyze promotion statuses
      console.log(`\n📋 Promotion Status Analysis:`);
      const statusCounts = { active: 0, upcoming: 0, 'used up': 0, expired: 0 };
      
      myPromotions.forEach(promo => {
        const status = getPromotionStatus(promo).status;
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      });
      
      console.log(`✅ Active: ${statusCounts.active} promotions`);
      console.log(`⏳ Upcoming: ${statusCounts.upcoming} promotions`);
      console.log(`🚫 Used Up: ${statusCounts['used up']} promotions`);
      console.log(`❌ Expired: ${statusCounts.expired} promotions`);
      
      // Test new sorting logic
      console.log(`\n🎯 Testing NEW Sorting Logic (Status Priority):`);
      
      const sortedPromotions = [...myPromotions].sort((a, b) => {
        // Priority 1: PUBLIC promotions first, PRIVATE promotions last
        if (a.type === 'PUBLIC' && b.type === 'PRIVATE') return -1;
        if (a.type === 'PRIVATE' && b.type === 'PUBLIC') return 1;
        
        // Priority 2: Within same type, sort by status priority
        const statusA = getPromotionStatus(a).status;
        const statusB = getPromotionStatus(b).status;
        
        // Status priority: active > upcoming > used up > expired
        const statusPriority = { 'active': 1, 'upcoming': 2, 'used up': 3, 'expired': 4 };
        const priorityA = statusPriority[statusA] || 5;
        const priorityB = statusPriority[statusB] || 5;
        
        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }
        
        // Priority 3: Within same status, sort by claimed date (newest first)
        return new Date(b.claimedAt || b.createdAt) - new Date(a.claimedAt || a.createdAt);
      });
      
      console.log(`📋 Sorted My Promotions Display Order:`);
      
      let currentSection = '';
      sortedPromotions.forEach((promo, i) => {
        const statusInfo = getPromotionStatus(promo);
        const section = `${promo.type} - ${statusInfo.status.toUpperCase()}`;
        
        if (section !== currentSection) {
          console.log(`\n--- ${section} ---`);
          currentSection = section;
        }
        
        const statusIcon = {
          'active': '✅',
          'upcoming': '⏳',
          'used up': '🚫',
          'expired': '❌'
        }[statusInfo.status] || '❓';
        
        const typeIcon = promo.type === 'PUBLIC' ? '🌐' : '🔒';
        
        console.log(`${i + 1}. ${statusIcon} ${typeIcon} ${promo.code} - ${promo.name}`);
        console.log(`   Status: ${statusInfo.message}`);
        console.log(`   Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
        console.log(`   Dates: ${new Date(promo.startDate).toLocaleDateString()} - ${new Date(promo.endDate).toLocaleDateString()}`);
      });
      
      // Verify sorting order
      console.log(`\n📊 Sorting Verification:`);
      
      const sections = [
        { type: 'PUBLIC', status: 'active', name: 'PUBLIC Active' },
        { type: 'PUBLIC', status: 'upcoming', name: 'PUBLIC Upcoming' },
        { type: 'PUBLIC', status: 'used up', name: 'PUBLIC Used Up' },
        { type: 'PUBLIC', status: 'expired', name: 'PUBLIC Expired' },
        { type: 'PRIVATE', status: 'active', name: 'PRIVATE Active' },
        { type: 'PRIVATE', status: 'upcoming', name: 'PRIVATE Upcoming' },
        { type: 'PRIVATE', status: 'used up', name: 'PRIVATE Used Up' },
        { type: 'PRIVATE', status: 'expired', name: 'PRIVATE Expired' }
      ];
      
      console.log(`Expected Order:`);
      sections.forEach((section, i) => {
        const count = sortedPromotions.filter(p => 
          p.type === section.type && getPromotionStatus(p).status === section.status
        ).length;
        
        if (count > 0) {
          console.log(`${i + 1}. ${section.name}: ${count} promotions`);
        }
      });
      
      // Check if upcoming promotions are in correct position
      console.log(`\n🎯 Upcoming Promotions Position Check:`);
      
      const upcomingPromotions = sortedPromotions.filter(p => getPromotionStatus(p).status === 'upcoming');
      
      if (upcomingPromotions.length > 0) {
        console.log(`⏳ Found ${upcomingPromotions.length} upcoming promotions:`);
        
        upcomingPromotions.forEach((promo, i) => {
          const position = sortedPromotions.indexOf(promo) + 1;
          console.log(`${i + 1}. ${promo.code} at position ${position}`);
        });
        
        // Check if upcoming comes after active but before used up
        const activeCount = sortedPromotions.filter(p => getPromotionStatus(p).status === 'active').length;
        const firstUpcomingPosition = sortedPromotions.findIndex(p => getPromotionStatus(p).status === 'upcoming') + 1;
        
        if (firstUpcomingPosition > activeCount) {
          console.log(`✅ Upcoming promotions correctly positioned after active promotions`);
        } else {
          console.log(`❌ Upcoming promotions positioning needs adjustment`);
        }
        
      } else {
        console.log(`ℹ️ No upcoming promotions found in current data`);
      }
      
      // Test different scenarios
      console.log(`\n📋 Sorting Logic Summary:`);
      console.log(`1️⃣ PUBLIC promotions come before PRIVATE`);
      console.log(`2️⃣ Within each type, status priority:`);
      console.log(`   ✅ Active (can use now)`);
      console.log(`   ⏳ Upcoming (coming soon)`);
      console.log(`   🚫 Used Up (exhausted personal limit)`);
      console.log(`   ❌ Expired (past end date)`);
      console.log(`3️⃣ Within same status, newest claimed first`);
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(70));
    console.log("🎯 UPCOMING PROMOTIONS SORTING SUMMARY:");
    console.log("✅ Improved sorting logic with status priority:");
    console.log("   🥇 Active promotions (highest priority)");
    console.log("   🥈 Upcoming promotions (second priority)");
    console.log("   🥉 Used up promotions (third priority)");
    console.log("   🏁 Expired promotions (lowest priority)");
    console.log("✅ Logical user experience:");
    console.log("   📈 Most useful promotions first");
    console.log("   ⏳ Future opportunities visible");
    console.log("   🚫 Exhausted options at bottom");
    console.log("   ❌ Expired promotions last");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testUpcomingPromotionsSorting();
