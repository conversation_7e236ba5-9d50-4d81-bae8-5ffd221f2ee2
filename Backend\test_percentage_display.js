require('dotenv').config();
const axios = require('axios');

// Test percentage display in progress bar
async function testPercentageDisplay() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  try {
    console.log("🎯 Testing Percentage Display in Progress Bar");
    console.log("=" .repeat(60));
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    console.log(`📊 Total promotions: ${response.data.promotions?.length || 0}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      const promotions = response.data.promotions;
      
      console.log("\n📋 Percentage Display Examples");
      console.log("-" .repeat(40));
      
      // Function to create visual progress bar with percentage
      const createVisualProgress = (current, total, width = 20) => {
        if (!total || total === 0) return 'N/A (Unlimited)';
        
        const percentage = Math.round((current / total) * 100);
        const filled = Math.round((current / total) * width);
        const empty = width - filled;
        
        let bar = '█'.repeat(filled) + '░'.repeat(empty);
        let color = '';
        
        if (percentage >= 100) color = '🔴'; // Red - exhausted
        else if (percentage >= 90) color = '🟠'; // Orange - almost full
        else if (percentage >= 70) color = '🟡'; // Yellow - high usage
        else if (percentage >= 50) color = '🔵'; // Blue - medium usage
        else color = '🟢'; // Green - low usage
        
        return `${color} [${bar}] ${percentage}%`;
      };
      
      // Test promotions with different usage levels
      const promotionsWithLimits = promotions.filter(p => p.usageLimit);
      
      console.log(`📊 Progress Bar Display Examples:`);
      
      promotionsWithLimits.forEach((promo, index) => {
        const usedCount = promo.usedCount || 0;
        const usageLimit = promo.usageLimit;
        const percentage = Math.round((usedCount / usageLimit) * 100);
        
        console.log(`\n${index + 1}. ${promo.code}`);
        console.log(`   Raw data: ${usedCount}/${usageLimit}`);
        console.log(`   Display: ${createVisualProgress(usedCount, usageLimit)}`);
        console.log(`   Frontend shows: "${percentage}%"`);
        
        // Determine status message
        let status = '';
        if (percentage >= 100) {
          status = '🚫 Exhausted';
        } else if (percentage >= 90) {
          status = '⚠️ Almost full';
        } else {
          status = '✅ Available';
        }
        console.log(`   Status: ${status}`);
      });
      
      // Test edge cases for percentage calculation
      console.log(`\n📋 Edge Cases for Percentage Display`);
      console.log("-" .repeat(40));
      
      const edgeCases = [
        { name: "Brand New", used: 0, limit: 100, expected: "0%" },
        { name: "Single Use", used: 1, limit: 100, expected: "1%" },
        { name: "Half Used", used: 50, limit: 100, expected: "50%" },
        { name: "Almost Full", used: 99, limit: 100, expected: "99%" },
        { name: "Exactly Full", used: 100, limit: 100, expected: "100%" },
        { name: "Small Limit", used: 2, limit: 3, expected: "67%" },
        { name: "Tiny Limit", used: 1, limit: 1, expected: "100%" },
        { name: "Large Numbers", used: 1500, limit: 2000, expected: "75%" }
      ];
      
      edgeCases.forEach((testCase, i) => {
        const percentage = Math.round((testCase.used / testCase.limit) * 100);
        console.log(`${i + 1}. ${testCase.name}: ${testCase.used}/${testCase.limit} → ${percentage}% (expected: ${testCase.expected})`);
        
        if (percentage.toString() + '%' === testCase.expected) {
          console.log(`   ✅ Correct`);
        } else {
          console.log(`   ❌ Mismatch`);
        }
      });
      
      // Test UI layout preview
      console.log(`\n📋 UI Layout Preview`);
      console.log("-" .repeat(40));
      
      console.log(`PromotionModal Card Layout:`);
      promotionsWithLimits.slice(0, 3).forEach((promo, i) => {
        const percentage = Math.round(((promo.usedCount || 0) / promo.usageLimit) * 100);
        
        console.log(`\n${i + 1}. ${promo.code} Card:`);
        console.log(`   ┌─────────────────────────────────────┐`);
        console.log(`   │ ${promo.name.padEnd(35)} │`);
        console.log(`   │ Your Usage: ${(promo.userUsedCount || 0)}/${promo.maxUsagePerUser || 1}${' '.repeat(20)} │`);
        console.log(`   │ 🌍 Global Usage:${' '.repeat(15)}${percentage}% │`);
        console.log(`   │ [████████████████████████████████] │`);
        console.log(`   │ ${percentage >= 100 ? '🚫 Exhausted' : percentage >= 90 ? '⚠️ Almost full' : '✅ Available'}${' '.repeat(20)} │`);
        console.log(`   └─────────────────────────────────────┘`);
      });
      
      // Test color coding
      console.log(`\n📋 Color Coding Test`);
      console.log("-" .repeat(40));
      
      const colorTests = [
        { percentage: 0, expected: 'success', color: '🟢' },
        { percentage: 25, expected: 'success', color: '🟢' },
        { percentage: 49, expected: 'success', color: '🟢' },
        { percentage: 50, expected: 'info', color: '🔵' },
        { percentage: 69, expected: 'info', color: '🔵' },
        { percentage: 70, expected: 'warning', color: '🟡' },
        { percentage: 89, expected: 'warning', color: '🟡' },
        { percentage: 90, expected: 'danger', color: '🔴' },
        { percentage: 100, expected: 'danger', color: '🔴' }
      ];
      
      console.log(`Progress Bar Color Variants:`);
      colorTests.forEach(test => {
        console.log(`${test.color} ${test.percentage}% → variant="${test.expected}"`);
      });
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(60));
    console.log("🎯 PERCENTAGE DISPLAY SUMMARY:");
    console.log("✅ Clean percentage display instead of raw numbers");
    console.log("✅ Format: 'XX%' (e.g., '75%', '100%', '0%')");
    console.log("✅ Simplified status messages:");
    console.log("   🚫 Exhausted (100%)");
    console.log("   ⚠️ Almost full (90-99%)");
    console.log("   ✅ Available (0-89%)");
    console.log("✅ Color-coded progress bars:");
    console.log("   🟢 Green: 0-49% (success)");
    console.log("   🔵 Blue: 50-69% (info)");
    console.log("   🟡 Yellow: 70-89% (warning)");
    console.log("   🔴 Red: 90-100% (danger)");
    console.log("✅ Clean, professional UI with better visual hierarchy");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testPercentageDisplay();
