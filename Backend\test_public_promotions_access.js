require('dotenv').config();
const axios = require('axios');

// Test public promotions access for different users
async function testPublicPromotionsAccess() {
  const baseURL = "http://localhost:5000/api";
  
  // Different user tokens for testing
  const users = [
    {
      name: "User 11 (Original)",
      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo",
      id: 11
    }
    // Add more users if you have their tokens
  ];
  
  try {
    console.log("🎯 Testing Public Promotions Access for Different Users");
    console.log("=" .repeat(70));
    
    for (const user of users) {
      console.log(`\n👤 Testing for ${user.name} (ID: ${user.id})`);
      console.log("-" .repeat(50));
      
      const response = await axios.get(`${baseURL}/promotions`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      
      console.log(`✅ API Response Status: ${response.status}`);
      
      if (response.data.promotions && response.data.promotions.length > 0) {
        const allPromotions = response.data.promotions;
        
        // Separate by type
        const publicPromotions = allPromotions.filter(p => p.type === 'PUBLIC');
        const privatePromotions = allPromotions.filter(p => p.type === 'PRIVATE');
        
        console.log(`📊 Total promotions: ${allPromotions.length}`);
        console.log(`🌐 PUBLIC promotions: ${publicPromotions.length}`);
        console.log(`🔒 PRIVATE promotions: ${privatePromotions.length}`);
        
        // Test My Promotions logic
        console.log(`\n📋 My Promotions Logic Test:`);
        
        // OLD logic (only claimed)
        const oldLogicPromotions = allPromotions.filter(p => p.isClaimed === true);
        
        // NEW logic (PUBLIC + claimed PRIVATE)
        const newLogicPromotions = allPromotions.filter(promotion => {
          if (promotion.type === 'PUBLIC') {
            return true; // All PUBLIC promotions
          }
          return promotion.isClaimed === true; // Only claimed PRIVATE
        });
        
        console.log(`❌ OLD Logic (only claimed): ${oldLogicPromotions.length} promotions`);
        console.log(`✅ NEW Logic (PUBLIC + claimed PRIVATE): ${newLogicPromotions.length} promotions`);
        
        // Show detailed breakdown
        console.log(`\n📊 Detailed Breakdown:`);
        
        console.log(`\n🌐 PUBLIC Promotions (should appear in My Promotions):`);
        publicPromotions.forEach((promo, i) => {
          console.log(`${i + 1}. ${promo.code} - ${promo.name}`);
          console.log(`   Claimed: ${promo.isClaimed ? 'Yes' : 'No'}`);
          console.log(`   User Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
          console.log(`   Available in OLD logic: ${promo.isClaimed ? 'Yes' : 'No'}`);
          console.log(`   Available in NEW logic: Yes`);
        });
        
        console.log(`\n🔒 PRIVATE Promotions (only if claimed):`);
        privatePromotions.forEach((promo, i) => {
          console.log(`${i + 1}. ${promo.code} - ${promo.name}`);
          console.log(`   Claimed: ${promo.isClaimed ? 'Yes' : 'No'}`);
          console.log(`   User Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
          console.log(`   Available in My Promotions: ${promo.isClaimed ? 'Yes' : 'No'}`);
        });
        
        // Test sorting with new logic
        console.log(`\n🎯 Sorting Test with NEW Logic:`);
        const sortedPromotions = [...newLogicPromotions].sort((a, b) => {
          // PUBLIC first, PRIVATE last
          if (a.type === 'PUBLIC' && b.type === 'PRIVATE') return -1;
          if (a.type === 'PRIVATE' && b.type === 'PUBLIC') return 1;
          
          // Available first, used up last
          const canUseA = a.userCanUse !== false;
          const canUseB = b.userCanUse !== false;
          
          if (canUseA && !canUseB) return -1;
          if (!canUseA && canUseB) return 1;
          
          return new Date(b.claimedAt || b.createdAt) - new Date(a.claimedAt || a.createdAt);
        });
        
        console.log(`📋 My Promotions Display Order:`);
        sortedPromotions.forEach((promo, i) => {
          const canUse = promo.userCanUse !== false;
          const statusIcon = canUse ? '✅' : '🚫';
          const typeIcon = promo.type === 'PUBLIC' ? '🌐' : '🔒';
          const claimedIcon = promo.isClaimed ? '📌' : '🆕';
          
          console.log(`${i + 1}. ${statusIcon} ${typeIcon} ${claimedIcon} ${promo.code}`);
          console.log(`   Type: ${promo.type} | Claimed: ${promo.isClaimed ? 'Yes' : 'No'}`);
          console.log(`   Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
        });
        
        // Compare results
        console.log(`\n📊 Comparison Summary:`);
        console.log(`OLD Logic would show: ${oldLogicPromotions.length} promotions`);
        console.log(`NEW Logic shows: ${newLogicPromotions.length} promotions`);
        console.log(`Improvement: +${newLogicPromotions.length - oldLogicPromotions.length} more promotions visible`);
        
        // Check if PUBLIC promotions are now visible
        const publicInOld = oldLogicPromotions.filter(p => p.type === 'PUBLIC').length;
        const publicInNew = newLogicPromotions.filter(p => p.type === 'PUBLIC').length;
        
        console.log(`PUBLIC promotions in OLD: ${publicInOld}`);
        console.log(`PUBLIC promotions in NEW: ${publicInNew}`);
        
        if (publicInNew > publicInOld) {
          console.log(`✅ SUCCESS: More PUBLIC promotions now visible!`);
        } else if (publicInNew === publicPromotions.length) {
          console.log(`✅ SUCCESS: All PUBLIC promotions are visible!`);
        }
        
      } else {
        console.log(`❌ No promotions returned for ${user.name}`);
      }
    }
    
    console.log("\n" + "=" .repeat(70));
    console.log("🎯 PUBLIC PROMOTIONS ACCESS SUMMARY:");
    console.log("✅ Fixed My Promotions logic:");
    console.log("   🌐 PUBLIC promotions: Always visible (no claim needed)");
    console.log("   🔒 PRIVATE promotions: Only visible when claimed");
    console.log("✅ Benefits:");
    console.log("   📈 More promotions visible to users");
    console.log("   🎯 Better user experience");
    console.log("   🌐 PUBLIC promotions work as intended");
    console.log("   🔒 PRIVATE promotions remain exclusive");
    console.log("✅ All users can now see PUBLIC promotions in My Promotions");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testPublicPromotionsAccess();
