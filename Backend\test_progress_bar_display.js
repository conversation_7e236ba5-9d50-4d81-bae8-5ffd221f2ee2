require('dotenv').config();
const axios = require('axios');

// Test progress bar display for global usage
async function testProgressBarDisplay() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  try {
    console.log("🎯 Testing Progress Bar Display for Global Usage");
    console.log("=" .repeat(60));
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    console.log(`📊 Total promotions: ${response.data.promotions?.length || 0}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      const promotions = response.data.promotions;
      
      console.log("\n📋 Progress Bar Visualization Test");
      console.log("-" .repeat(40));
      
      // Function to create ASCII progress bar
      const createProgressBar = (current, total, width = 20) => {
        if (!total || total === 0) return 'N/A (Unlimited)';
        
        const percentage = (current / total) * 100;
        const filled = Math.round((current / total) * width);
        const empty = width - filled;
        
        let bar = '█'.repeat(filled) + '░'.repeat(empty);
        let color = '';
        
        if (percentage >= 100) color = '🔴'; // Red - exhausted
        else if (percentage >= 90) color = '🟠'; // Orange - almost exhausted  
        else if (percentage >= 70) color = '🟡'; // Yellow - high usage
        else if (percentage >= 50) color = '🔵'; // Blue - medium usage
        else color = '🟢'; // Green - low usage
        
        return `${color} [${bar}] ${percentage.toFixed(1)}%`;
      };
      
      // Test different usage scenarios
      const promotionsWithLimits = promotions.filter(p => p.usageLimit);
      
      console.log(`📊 Promotions with usage limits: ${promotionsWithLimits.length}`);
      
      promotionsWithLimits.forEach((promo, index) => {
        const usedCount = promo.usedCount || 0;
        const usageLimit = promo.usageLimit;
        const percentage = (usedCount / usageLimit) * 100;
        const remaining = usageLimit - usedCount;
        
        console.log(`\n${index + 1}. ${promo.code} - ${promo.name}`);
        console.log(`   Usage: ${usedCount}/${usageLimit} (${remaining} left)`);
        console.log(`   Progress: ${createProgressBar(usedCount, usageLimit)}`);
        
        // Determine progress bar variant
        let variant = 'success';
        let status = '';
        
        if (percentage >= 100) {
          variant = 'danger';
          status = '🚫 Promotion exhausted';
        } else if (percentage >= 90) {
          variant = 'danger';
          status = `⚠️ Almost exhausted (${Math.round(percentage)}% used)`;
        } else if (percentage >= 70) {
          variant = 'warning';
          status = `📊 ${Math.round(percentage)}% used`;
        } else if (percentage >= 50) {
          variant = 'info';
          status = `📊 ${Math.round(percentage)}% used`;
        } else {
          variant = 'success';
          status = '✅ Plenty available';
        }
        
        console.log(`   Variant: ${variant}`);
        console.log(`   Status: ${status}`);
      });
      
      // Test unlimited promotions
      const unlimitedPromotions = promotions.filter(p => !p.usageLimit);
      console.log(`\n📊 Unlimited promotions: ${unlimitedPromotions.length}`);
      
      if (unlimitedPromotions.length > 0) {
        console.log(`   Examples:`);
        unlimitedPromotions.slice(0, 3).forEach((promo, i) => {
          console.log(`   ${i + 1}. ${promo.code}: ${promo.usedCount || 0} uses (Unlimited)`);
        });
      }
      
      // Test edge cases
      console.log(`\n📋 Edge Cases for Progress Bar`);
      console.log("-" .repeat(40));
      
      const edgeCases = [
        { name: "New Promotion", used: 0, limit: 100 },
        { name: "Half Used", used: 50, limit: 100 },
        { name: "Almost Full", used: 95, limit: 100 },
        { name: "Exactly Full", used: 100, limit: 100 },
        { name: "Small Limit", used: 2, limit: 3 },
        { name: "Single Use", used: 0, limit: 1 }
      ];
      
      edgeCases.forEach((testCase, i) => {
        const percentage = (testCase.used / testCase.limit) * 100;
        console.log(`${i + 1}. ${testCase.name}: ${createProgressBar(testCase.used, testCase.limit)}`);
        console.log(`   Data: ${testCase.used}/${testCase.limit} (${percentage.toFixed(1)}%)`);
      });
      
      // Frontend implementation preview
      console.log(`\n📋 Frontend Implementation Preview`);
      console.log("-" .repeat(40));
      
      console.log(`React Bootstrap ProgressBar props:`);
      promotionsWithLimits.slice(0, 3).forEach((promo, i) => {
        const usedCount = promo.usedCount || 0;
        const usageLimit = promo.usageLimit;
        const percentage = (usedCount / usageLimit) * 100;
        
        let variant = 'success';
        if (percentage >= 90) variant = 'danger';
        else if (percentage >= 70) variant = 'warning';
        else if (percentage >= 50) variant = 'info';
        
        console.log(`\n${i + 1}. ${promo.code}:`);
        console.log(`   <ProgressBar`);
        console.log(`     now={${percentage.toFixed(1)}}`);
        console.log(`     variant="${variant}"`);
        console.log(`     style={{ height: '8px', backgroundColor: 'rgba(255,255,255,0.2)' }}`);
        console.log(`   />`);
        console.log(`   Label: "${usedCount}/${usageLimit} (${usageLimit - usedCount} left)"`);
      });
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(60));
    console.log("🎯 PROGRESS BAR DISPLAY SUMMARY:");
    console.log("✅ Visual progress bar for global usage");
    console.log("✅ Color-coded variants:");
    console.log("   🟢 Green (success): 0-49% used");
    console.log("   🔵 Blue (info): 50-69% used");
    console.log("   🟡 Yellow (warning): 70-89% used");
    console.log("   🔴 Red (danger): 90-100% used");
    console.log("✅ Smart status messages:");
    console.log("   📊 Usage percentage for high usage");
    console.log("   ⚠️ Almost exhausted warning");
    console.log("   🚫 Exhausted notification");
    console.log("✅ Remaining count display");
    console.log("✅ Better visual understanding of promotion availability");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testProgressBarDisplay();
