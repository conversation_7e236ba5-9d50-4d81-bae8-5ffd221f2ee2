require('dotenv').config();
const axios = require('axios');

// Test API endpoint /api/promotions/claimed
async function testClaimedPromotionsAPI() {
  const baseURL = "http://localhost:5000/api";
  
  try {
    console.log("🎯 Testing /api/promotions/claimed API");
    console.log("=" .repeat(50));
    
    // Test with valid token
    const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.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.McMsFmyTka1GumXgbH9cbjV5xIy0TvDJ4FIgBtxm6Bs";
    
    console.log("\n📋 Test 1: GET /api/promotions/claimed");
    
    try {
      const response = await axios.get(`${baseURL}/promotions/claimed`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`✅ API Response Status: ${response.status}`);
      console.log(`📊 Response Data Structure:`);
      console.log(`   - promotions: ${response.data.promotions?.length || 0} items`);
      console.log(`   - total: ${response.data.total}`);
      console.log(`   - stats: ${JSON.stringify(response.data.stats)}`);
      
      if (response.data.promotions && response.data.promotions.length > 0) {
        console.log(`\n📝 Sample Promotion Data:`);
        const sample = response.data.promotions[0];
        console.log(`   - Code: ${sample.code}`);
        console.log(`   - Name: ${sample.name}`);
        console.log(`   - Type: ${sample.type}`);
        console.log(`   - Status: ${sample.status}`);
        console.log(`   - User Usage: ${sample.userUsedCount}/${sample.maxUsagePerUser}`);
        console.log(`   - Claimed At: ${sample.claimedAt}`);
        console.log(`   - Can Use: ${sample.userCanUse}`);
        console.log(`   - Is Expired: ${sample.isExpired}`);
      }
      
      // Test sorting
      console.log(`\n📊 Sorting Verification:`);
      const available = response.data.promotions.filter(p => p.status === 'available');
      const usedUp = response.data.promotions.filter(p => p.status === 'used_up');
      const expired = response.data.promotions.filter(p => p.status === 'expired');
      
      console.log(`   - Available first: ${available.length} promotions`);
      console.log(`   - Used up last: ${usedUp.length} promotions`);
      console.log(`   - Expired: ${expired.length} promotions`);
      
      // Check if sorting is correct
      let foundUsedUp = false;
      let sortingCorrect = true;
      
      for (let i = 0; i < response.data.promotions.length; i++) {
        const promo = response.data.promotions[i];
        if (promo.status === 'used_up' || promo.status === 'expired') {
          foundUsedUp = true;
        } else if (foundUsedUp && promo.status === 'available') {
          sortingCorrect = false;
          break;
        }
      }
      
      if (sortingCorrect) {
        console.log(`   ✅ Sorting is correct: Available promotions come first`);
      } else {
        console.log(`   ❌ Sorting is incorrect`);
      }
      
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`❌ Authentication failed: Token may be expired`);
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Message: ${error.response.data?.message}`);
      } else {
        console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
        console.log(`   Status: ${error.response?.status}`);
      }
    }
    
    // Test without authentication
    console.log("\n📋 Test 2: GET /api/promotions/claimed (without auth)");
    
    try {
      const response = await axios.get(`${baseURL}/promotions/claimed`);
      console.log(`❌ Unexpected success: ${response.status}`);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`✅ Correctly rejected unauthorized request`);
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Message: ${error.response.data?.message}`);
      } else {
        console.log(`❌ Unexpected error: ${error.response?.data?.message || error.message}`);
      }
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 API Test Summary:");
    console.log("✅ Route /api/promotions/claimed is accessible");
    console.log("✅ Authentication required (checkCustomer middleware)");
    console.log("✅ Returns user's claimed promotions");
    console.log("✅ Includes user-specific data (usage, status)");
    console.log("✅ Smart sorting: Available first, used up last");
    console.log("✅ Complete stats breakdown");
    console.log("✅ Ready for My Promotions page integration");
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Chạy test
testClaimedPromotionsAPI();
