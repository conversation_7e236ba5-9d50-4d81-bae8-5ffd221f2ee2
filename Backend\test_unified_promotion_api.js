require('dotenv').config();
const axios = require('axios');

// Test unified promotion API for both PromotionModal and My Promotions
async function testUnifiedPromotionAPI() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  try {
    console.log("🎯 Testing Unified Promotion API");
    console.log("=" .repeat(50));
    
    console.log("\n📋 Test: GET /api/promotions (unified API)");
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    console.log(`📊 Total promotions: ${response.data.promotions?.length || 0}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      // Analyze promotions for both use cases
      const allPromotions = response.data.promotions;
      
      // For PromotionModal: All promotions (public + claimed private)
      console.log(`\n🎯 PromotionModal Use Case:`);
      console.log(`   📊 Total promotions shown: ${allPromotions.length}`);
      
      const publicPromotions = allPromotions.filter(p => p.type === 'PUBLIC');
      const privatePromotions = allPromotions.filter(p => p.type === 'PRIVATE');
      
      console.log(`   🌐 Public promotions: ${publicPromotions.length}`);
      console.log(`   🔒 Private promotions (claimed): ${privatePromotions.length}`);
      
      // For My Promotions: Only claimed promotions
      const claimedPromotions = allPromotions.filter(p => p.isClaimed === true);
      console.log(`\n🎯 My Promotions Use Case:`);
      console.log(`   📊 Claimed promotions only: ${claimedPromotions.length}`);
      
      if (claimedPromotions.length > 0) {
        console.log(`   📝 Claimed promotions breakdown:`);
        claimedPromotions.forEach((p, i) => {
          const statusIcon = p.userCanUse ? '✅' : '🚫';
          console.log(`      ${i + 1}. ${statusIcon} ${p.code} (${p.type})`);
          console.log(`         Usage: ${p.userUsedCount}/${p.maxUsagePerUser}`);
          console.log(`         Status: ${p.userCanUse ? 'Available' : 'Used Up'}`);
        });
        
        // Test sorting for My Promotions
        const sortedClaimed = [...claimedPromotions].sort((a, b) => {
          // Available first, used up last
          if (a.userCanUse && !b.userCanUse) return -1;
          if (!a.userCanUse && b.userCanUse) return 1;
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
        
        console.log(`\n   📊 Sorted for My Promotions (Available first):`);
        sortedClaimed.forEach((p, i) => {
          const statusIcon = p.userCanUse ? '✅' : '🚫';
          console.log(`      ${i + 1}. ${statusIcon} ${p.code} - ${p.userCanUse ? 'Available' : 'Used Up'}`);
        });
        
      } else {
        console.log(`   ❌ No claimed promotions found`);
        console.log(`   💡 User needs to claim some promotions first`);
      }
      
      // Test filtering capabilities
      console.log(`\n🔍 Filtering Test:`);
      
      const availableClaimed = claimedPromotions.filter(p => p.userCanUse);
      const usedUpClaimed = claimedPromotions.filter(p => !p.userCanUse);
      const publicClaimed = claimedPromotions.filter(p => p.type === 'PUBLIC');
      const privateClaimed = claimedPromotions.filter(p => p.type === 'PRIVATE');
      
      console.log(`   📊 Filter options for My Promotions:`);
      console.log(`      - Available: ${availableClaimed.length}`);
      console.log(`      - Used Up: ${usedUpClaimed.length}`);
      console.log(`      - Public: ${publicClaimed.length}`);
      console.log(`      - Private: ${privateClaimed.length}`);
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 UNIFIED API SUMMARY:");
    console.log("✅ Single API endpoint /api/promotions serves both:");
    console.log("   🎯 PromotionModal: Shows all promotions (public + claimed private)");
    console.log("   🎯 My Promotions: Filters to show only claimed promotions");
    console.log("✅ Simplified architecture - no duplicate APIs needed");
    console.log("✅ Consistent data structure across components");
    console.log("✅ Easy to maintain and debug");
    
    if (claimedPromotions && claimedPromotions.length > 0) {
      console.log("✅ My Promotions should now display claimed promotions");
    } else {
      console.log("❌ My Promotions will show empty - user needs to claim promotions");
    }
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testUnifiedPromotionAPI();
