require("dotenv").config();
const axios = require("axios");
const mongoose = require("mongoose");

// Test API thực tế với authentication
async function testRealAPI() {
  const baseURL = "http://localhost:5000/api";
  
  try {
    console.log("🧪 Testing maxUsagePerUser feature with real API");
    
    // Bước 1: Đăng nhập để lấy token
    console.log("\n📋 Step 1: Login to get authentication token");
    const loginResponse = await axios.post(`${baseURL}/auth/login_customer`, {
      email: "<EMAIL>", // Sử dụng customer account từ seed
      password: "123456"
    });
    
    const token = loginResponse.data.Data.token;
    console.log("✅ Login successful, got token");
    
    // Bước 2: Tạo promotion test với maxUsagePerUser = 2
    console.log("\n📋 Step 2: Create test promotion");
    const promotionData = {
      code: "APITEST",
      name: "API Test Promotion",
      description: "Test promotion for API testing",
      discountType: "FIXED_AMOUNT",
      discountValue: 15,
      minOrderAmount: 100,
      startDate: "2025-01-01",
      endDate: "2025-12-31",
      usageLimit: 50,
      maxUsagePerUser: 2,
      isActive: true
    };
    
    try {
      const createResponse = await axios.post(`${baseURL}/promotions`, promotionData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      console.log("✅ Promotion created successfully");
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes("duplicate")) {
        console.log("⚠️ Promotion already exists, continuing with test");
      } else {
        throw error;
      }
    }
    
    // Bước 3: Test apply promotion lần đầu tiên
    console.log("\n📋 Step 3: Apply promotion first time");
    try {
      const response1 = await axios.post(`${baseURL}/promotions/apply`, {
        code: "APITEST",
        orderAmount: 150
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      console.log("✅ First application successful:");
      console.log(`   Discount: $${response1.data.discount}`);
      console.log(`   Message: ${response1.data.message}`);
    } catch (error) {
      console.log("❌ First application failed:", error.response?.data?.message || error.message);
    }
    
    // Bước 4: Test apply promotion lần thứ hai (should work)
    console.log("\n📋 Step 4: Apply promotion second time");
    try {
      const response2 = await axios.post(`${baseURL}/promotions/apply`, {
        code: "APITEST",
        orderAmount: 150
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      console.log("✅ Second application successful:");
      console.log(`   Discount: $${response2.data.discount}`);
      console.log(`   Message: ${response2.data.message}`);
    } catch (error) {
      console.log("❌ Second application failed:", error.response?.data?.message || error.message);
    }
    
    // Bước 5: Test apply promotion lần thứ ba (should fail)
    console.log("\n📋 Step 5: Apply promotion third time (should fail)");
    try {
      const response3 = await axios.post(`${baseURL}/promotions/apply`, {
        code: "APITEST",
        orderAmount: 150
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      console.log("⚠️ Third application successful (unexpected):");
      console.log(`   Discount: $${response3.data.discount}`);
      console.log(`   Message: ${response3.data.message}`);
    } catch (error) {
      console.log("✅ Third application failed as expected:");
      console.log(`   Error: ${error.response?.data?.message || error.message}`);
    }
    
    // Bước 6: Kiểm tra database để xác nhận
    console.log("\n📋 Step 6: Verify database state");
    await mongoose.connect(process.env.MONGODB_URI || "mongodb://127.0.0.1:27017/My_Uroom");
    
    const PromotionUser = require("./src/models/PromotionUser");
    const Promotion = require("./src/models/Promotion");
    
    const promotion = await Promotion.findOne({ code: "APITEST" });
    const promotionUsers = await PromotionUser.find({ promotionId: promotion._id });
    
    console.log(`📊 Promotion total usage: ${promotion.usedCount}`);
    console.log(`📊 Number of users: ${promotionUsers.length}`);
    promotionUsers.forEach(pu => {
      console.log(`   User ${pu.userId}: ${pu.usedCount} times`);
    });
    
    await mongoose.disconnect();
    console.log("\n✅ API test completed successfully!");
    
  } catch (error) {
    console.error("❌ API test failed:", error.response?.data || error.message);
  }
}

// Chạy test
testRealAPI();
