require("dotenv").config();
const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
const PromotionUser = require("./src/models/PromotionUser");

// Test script để kiểm tra tính năng maxUsagePerUser
async function testMaxUsagePerUser() {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGODB_URI || "mongodb://127.0.0.1:27017/My_Uroom");
    console.log("✅ Connected to MongoDB");

    // Tìm promotion WELCOME50K (maxUsagePerUser = 1)
    const promotion = await Promotion.findOne({ code: "WELCOME50K" });
    if (!promotion) {
      console.log("❌ Promotion WELCOME50K not found");
      return;
    }

    console.log(`📋 Testing promotion: ${promotion.code}`);
    console.log(`📊 maxUsagePerUser: ${promotion.maxUsagePerUser}`);
    console.log(`📊 Current usedCount: ${promotion.usedCount}`);

    // Test user ID (giả sử user có ID = 1)
    const testUserId = 1;

    // Kiểm tra PromotionUser hiện tại
    let promotionUser = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId: testUserId
    });

    console.log(`👤 Current user usage:`, promotionUser ? promotionUser.usedCount : 0);

    // Test case 1: Lần đầu tiên sử dụng (should work)
    if (!promotionUser || promotionUser.usedCount < promotion.maxUsagePerUser) {
      console.log("\n🧪 Test Case 1: First time usage (should work)");
      
      // Tạo hoặc cập nhật PromotionUser
      promotionUser = await PromotionUser.findOneAndUpdate(
        { promotionId: promotion._id, userId: testUserId },
        { 
          $inc: { usedCount: 1 },
          $set: { 
            lastUsedAt: new Date(),
            lastReservationId: new mongoose.Types.ObjectId() // Fake reservation ID
          }
        },
        { upsert: true, new: true }
      );

      // Tăng usedCount tổng
      await Promotion.findByIdAndUpdate(promotion._id, { $inc: { usedCount: 1 } });

      console.log("✅ First usage successful");
      console.log(`📊 User usage count: ${promotionUser.usedCount}`);
    }

    // Test case 2: Lần thứ hai sử dụng (should fail)
    console.log("\n🧪 Test Case 2: Second time usage (should fail)");
    
    // Kiểm tra lại PromotionUser
    promotionUser = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId: testUserId
    });

    if (promotionUser && promotionUser.usedCount >= promotion.maxUsagePerUser) {
      console.log("❌ User has reached maximum usage limit - This is expected!");
      console.log(`📊 User usage: ${promotionUser.usedCount}/${promotion.maxUsagePerUser}`);
    } else {
      console.log("⚠️ User can still use promotion - This might be unexpected");
    }

    // Test case 3: Kiểm tra với user khác (should work)
    console.log("\n🧪 Test Case 3: Different user usage (should work)");
    const anotherUserId = 2;
    
    const anotherUserPromotion = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId: anotherUserId
    });

    if (!anotherUserPromotion || anotherUserPromotion.usedCount < promotion.maxUsagePerUser) {
      console.log("✅ Another user can use the promotion - This is expected!");
    } else {
      console.log("❌ Another user cannot use promotion - This is unexpected");
    }

    // Hiển thị thống kê cuối
    const updatedPromotion = await Promotion.findById(promotion._id);
    const allUsages = await PromotionUser.find({ promotionId: promotion._id });
    
    console.log("\n📊 Final Statistics:");
    console.log(`Total promotion usage: ${updatedPromotion.usedCount}`);
    console.log(`Number of users used: ${allUsages.length}`);
    console.log(`User usages:`, allUsages.map(u => `User ${u.userId}: ${u.usedCount} times`));

    console.log("\n✅ Test completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Chạy test
testMaxUsagePerUser();
