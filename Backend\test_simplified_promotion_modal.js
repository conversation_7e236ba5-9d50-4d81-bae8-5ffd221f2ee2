require('dotenv').config();
const axios = require('axios');

// Test tính năng PromotionModal đã được đơn giản hóa
async function testSimplifiedPromotionModal() {
  const baseURL = "http://localhost:5000/api";
  
  try {
    console.log("🎯 Testing Simplified PromotionModal");
    console.log("=" .repeat(50));
    
    // Test 1: Kiểm tra API trả về cả public và claimed promotions
    console.log("\n📋 Test 1: Check API returns public + claimed promotions");
    
    // Test without authentication (chỉ public promotions)
    const publicResponse = await axios.get(`${baseURL}/promotions`);
    console.log(`✅ Without auth: ${publicResponse.data.promotions.length} promotions`);
    
    // Test with authentication (public + claimed private promotions)
    const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.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.McMsFmyTka1GumXgbH9cbjV5xIy0TvDJ4FIgBtxm6Bs";
    
    try {
      const authResponse = await axios.get(`${baseURL}/promotions`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`✅ With auth: ${authResponse.data.promotions.length} promotions`);
      
      // Check for claimed private promotions
      const privatePromotions = authResponse.data.promotions.filter(p => p.type === 'PRIVATE');
      console.log(`🔒 Private promotions in list: ${privatePromotions.length}`);
      
      privatePromotions.forEach(p => {
        console.log(`   - ${p.code}: claimed=${p.isClaimed}, usage=${p.userUsedCount}/${p.maxUsagePerUser}`);
      });
      
    } catch (error) {
      console.log(`❌ Auth request failed: ${error.response?.data?.message || error.message}`);
    }
    
    // Test 2: Auto-claim when applying promotion
    console.log("\n📋 Test 2: Auto-claim when applying promotion");
    
    try {
      const applyResponse = await axios.post(`${baseURL}/promotions/apply`, {
        code: 'HIDDEN50',
        orderAmount: 200
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`✅ HIDDEN50: Applied successfully - Discount: $${applyResponse.data.discount}`);
      console.log(`   Message: ${applyResponse.data.message}`);
      
    } catch (error) {
      console.log(`❌ HIDDEN50: ${error.response?.data?.message || error.message}`);
    }
    
    // Test 3: Check if promotion was auto-claimed
    console.log("\n📋 Test 3: Verify auto-claim worked");
    
    try {
      const verifyResponse = await axios.get(`${baseURL}/promotions`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      const hiddenPromo = verifyResponse.data.promotions.find(p => p.code === 'HIDDEN50');
      if (hiddenPromo) {
        console.log(`✅ HIDDEN50 found in promotion list:`);
        console.log(`   - Type: ${hiddenPromo.type}`);
        console.log(`   - Claimed: ${hiddenPromo.isClaimed}`);
        console.log(`   - Usage: ${hiddenPromo.userUsedCount}/${hiddenPromo.maxUsagePerUser}`);
        console.log(`   - Can use: ${hiddenPromo.userCanUse}`);
      } else {
        console.log(`❌ HIDDEN50 not found in promotion list`);
      }
      
    } catch (error) {
      console.log(`❌ Verify failed: ${error.response?.data?.message || error.message}`);
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 Simplified PromotionModal Summary:");
    console.log("✅ Single API endpoint returns public + claimed promotions");
    console.log("✅ No separate tabs needed - unified promotion list");
    console.log("✅ Auto-claim when applying promotions");
    console.log("✅ Simple input field for manual code entry");
    console.log("✅ Clean, streamlined user experience");
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Chạy test
testSimplifiedPromotionModal();
