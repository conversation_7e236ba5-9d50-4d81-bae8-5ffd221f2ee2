require('dotenv').config();
const axios = require('axios');

// Test usage display differentiation
async function testUsageDisplay() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  try {
    console.log("🎯 Testing Usage Display Differentiation");
    console.log("=" .repeat(60));
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    console.log(`📊 Total promotions: ${response.data.promotions?.length || 0}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      const promotions = response.data.promotions;
      
      console.log("\n📋 Usage Information Analysis");
      console.log("-" .repeat(40));
      
      promotions.forEach((promo, index) => {
        console.log(`\n${index + 1}. ${promo.code} - ${promo.name}`);
        console.log(`   Type: ${promo.type} | Claimed: ${promo.isClaimed ? 'Yes' : 'No'}`);
        
        // User-specific usage
        console.log(`   👤 Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
        const userCanUse = (promo.userUsedCount || 0) < (promo.maxUsagePerUser || 1);
        console.log(`   👤 You can use: ${userCanUse ? 'Yes' : 'No (limit reached)'}`);
        
        // Global usage
        console.log(`   🌍 Global Usage: ${promo.usedCount || 0}/${promo.usageLimit || 'Unlimited'}`);
        const globalAvailable = !promo.usageLimit || (promo.usedCount || 0) < promo.usageLimit;
        console.log(`   🌍 Globally available: ${globalAvailable ? 'Yes' : 'No (exhausted)'}`);
        
        // Overall status
        const canApply = userCanUse && globalAvailable && promo.isActive;
        console.log(`   ✅ Can apply: ${canApply ? 'YES' : 'NO'}`);
        
        if (!canApply) {
          const reasons = [];
          if (!userCanUse) reasons.push('User limit reached');
          if (!globalAvailable) reasons.push('Global limit reached');
          if (!promo.isActive) reasons.push('Promotion inactive');
          console.log(`   ❌ Reasons: ${reasons.join(', ')}`);
        }
      });
      
      // Test UI display format
      console.log("\n📋 UI Display Format Examples");
      console.log("-" .repeat(40));
      
      const claimedPromotions = promotions.filter(p => p.isClaimed);
      
      console.log("\n🎯 PromotionModal Display:");
      promotions.slice(0, 3).forEach((promo, i) => {
        console.log(`${i + 1}. ${promo.code}`);
        console.log(`   Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
        if (promo.usageLimit) {
          console.log(`   Global Usage: ${promo.usedCount || 0}/${promo.usageLimit}`);
        }
        console.log(`   Status: ${promo.userCanUse ? 'Available' : 'Limit reached'}`);
      });
      
      console.log("\n🎯 My Promotions Display:");
      claimedPromotions.slice(0, 3).forEach((promo, i) => {
        console.log(`${i + 1}. ${promo.code}`);
        console.log(`   Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
        console.log(`   Global Usage: ${promo.usedCount || 0}/${promo.usageLimit || 'Unlimited'}`);
        console.log(`   Claimed: ${new Date(promo.claimedAt || promo.createdAt).toLocaleDateString()}`);
        console.log(`   Status: ${promo.userCanUse ? 'Available' : 'Used up'}`);
      });
      
      // Test edge cases
      console.log("\n📋 Edge Cases Analysis");
      console.log("-" .repeat(40));
      
      const userLimitReached = promotions.filter(p => 
        (p.userUsedCount || 0) >= (p.maxUsagePerUser || 1)
      );
      
      const globalLimitReached = promotions.filter(p => 
        p.usageLimit && (p.usedCount || 0) >= p.usageLimit
      );
      
      const unlimitedGlobal = promotions.filter(p => !p.usageLimit);
      
      console.log(`📊 Edge Cases Summary:`);
      console.log(`   👤 User limit reached: ${userLimitReached.length} promotions`);
      console.log(`   🌍 Global limit reached: ${globalLimitReached.length} promotions`);
      console.log(`   ♾️ Unlimited global usage: ${unlimitedGlobal.length} promotions`);
      
      if (userLimitReached.length > 0) {
        console.log(`\n   👤 User limit reached examples:`);
        userLimitReached.slice(0, 2).forEach(p => {
          console.log(`      - ${p.code}: ${p.userUsedCount}/${p.maxUsagePerUser} (user exhausted)`);
        });
      }
      
      if (globalLimitReached.length > 0) {
        console.log(`\n   🌍 Global limit reached examples:`);
        globalLimitReached.slice(0, 2).forEach(p => {
          console.log(`      - ${p.code}: ${p.usedCount}/${p.usageLimit} (globally exhausted)`);
        });
      }
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(60));
    console.log("🎯 USAGE DISPLAY SUMMARY:");
    console.log("✅ Clear differentiation between user and global usage");
    console.log("✅ PromotionModal shows:");
    console.log("   👤 Your Usage: X/Y (personal limit)");
    console.log("   🌍 Global Usage: A/B (total usage across all users)");
    console.log("✅ My Promotions shows:");
    console.log("   👤 Your Usage: X/Y (personal limit)");
    console.log("   🌍 Global Usage: A/B or A/Unlimited");
    console.log("   📅 Claimed date information");
    console.log("✅ Clear status indicators for both limits");
    console.log("✅ Better user understanding of promotion availability");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testUsageDisplay();
