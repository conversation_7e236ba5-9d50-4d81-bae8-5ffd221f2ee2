require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/user');
const bcrypt = require('bcryptjs');

async function createTestUser() {
  await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
  
  // Xóa user test cũ nếu có
  await User.deleteOne({ email: '<EMAIL>' });
  
  // Tạo user mới
  const hashedPassword = await bcrypt.hash('123456', 10);
  
  const testUser = new User({
    name: 'Test User',
    email: '<EMAIL>',
    password: hashedPassword,
    role: 'CUSTOMER',
    isVerified: true,
    phoneNumber: '0123456789',
    address: 'Test Address'
  });
  
  const saved = await testUser.save();
  console.log('Created test user:');
  console.log('ID:', saved._id);
  console.log('Email:', saved.email);
  console.log('Role:', saved.role);
  console.log('Verified:', saved.isVerified);
  
  // Test login
  const testPassword = '123456';
  const isMatch = await bcrypt.compare(testPassword, saved.password);
  console.log(`Password test: ${isMatch ? '✅ Match' : '❌ No match'}`);
  
  await mongoose.disconnect();
  console.log('Test user created successfully!');
}

createTestUser();
