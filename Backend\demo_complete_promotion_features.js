require('dotenv').config();
const mongoose = require('mongoose');
const PromotionUser = require('./src/models/PromotionUser');
const Promotion = require('./src/models/promotion');

// Demo toàn bộ tính năng promotion system
async function demoCompletePromotionFeatures() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
    
    console.log("🎯 COMPLETE PROMOTION SYSTEM DEMO");
    console.log("=" .repeat(60));
    
    const userId = 11; // Test user ID
    
    // Demo 1: Public vs Private Promotions
    console.log("\n📋 DEMO 1: Public vs Private Promotions");
    console.log("-" .repeat(40));
    
    const allPromotions = await Promotion.find({ isActive: true });
    const publicPromotions = allPromotions.filter(p => p.type === 'PUBLIC');
    const privatePromotions = allPromotions.filter(p => p.type === 'PRIVATE');
    
    console.log(`📊 Total promotions: ${allPromotions.length}`);
    console.log(`🌐 PUBLIC promotions: ${publicPromotions.length} (visible in promotion list)`);
    console.log(`🔒 PRIVATE promotions: ${privatePromotions.length} (hidden, code-only access)`);
    
    console.log(`\n🌐 PUBLIC Promotions:`);
    publicPromotions.slice(0, 5).forEach((p, i) => {
      console.log(`${i + 1}. ${p.code} - ${p.name}`);
    });
    
    console.log(`\n🔒 PRIVATE Promotions:`);
    privatePromotions.forEach((p, i) => {
      console.log(`${i + 1}. ${p.code} - ${p.name}`);
    });
    
    // Demo 2: Claim Promotions
    console.log("\n📋 DEMO 2: Claim Promotion Feature");
    console.log("-" .repeat(40));
    
    // Claim some promotions
    const promotionsToTest = ['SECRET20', 'WELCOME50K', 'INSIDER15'];
    
    for (const code of promotionsToTest) {
      try {
        const promotion = await Promotion.findOne({ code: code });
        if (!promotion) continue;
        
        let promotionUser = await PromotionUser.findOne({ 
          promotionId: promotion._id, 
          userId: userId 
        });
        
        if (promotionUser && promotionUser.isClaimed) {
          console.log(`⚠️ ${code}: Already claimed (${promotion.type})`);
        } else {
          if (!promotionUser) {
            promotionUser = new PromotionUser({
              promotionId: promotion._id,
              userId: userId
            });
          }
          
          await promotionUser.claimPromotion();
          console.log(`✅ ${code}: Successfully claimed (${promotion.type})`);
        }
      } catch (error) {
        console.log(`❌ ${code}: Error - ${error.message}`);
      }
    }
    
    // Demo 3: User's Claimed Promotions with Status
    console.log("\n📋 DEMO 3: User's Claimed Promotions");
    console.log("-" .repeat(40));
    
    const claimedPromotions = await PromotionUser.find({ 
      userId: userId,
      isClaimed: true 
    }).populate('promotionId');
    
    let available = 0, used = 0, expired = 0;
    
    console.log(`📝 User's Promotion Collection:`);
    claimedPromotions.forEach((pu, index) => {
      if (!pu.promotionId) return;
      
      const promotion = pu.promotionId;
      const now = new Date();
      const isValid = now >= promotion.startDate && now <= promotion.endDate;
      const canUse = pu.usedCount < (promotion.maxUsagePerUser || 1);
      
      let status = 'available';
      let statusIcon = '✅';
      if (!isValid) {
        status = 'expired';
        statusIcon = '⏰';
        expired++;
      } else if (!canUse) {
        status = 'used_up';
        statusIcon = '🚫';
        used++;
      } else {
        available++;
      }
      
      console.log(`${index + 1}. ${statusIcon} ${promotion.code} - ${promotion.name}`);
      console.log(`   Type: ${promotion.type} | Status: ${status.toUpperCase()}`);
      console.log(`   Usage: ${pu.usedCount}/${promotion.maxUsagePerUser} | Claimed: ${pu.claimedAt.toLocaleDateString()}`);
      
      if (promotion.discountType === 'PERCENTAGE') {
        console.log(`   Discount: ${promotion.discountValue}% (max $${promotion.maxDiscountAmount || 'unlimited'})`);
      } else {
        console.log(`   Discount: $${promotion.discountValue}`);
      }
      console.log('');
    });
    
    console.log(`📊 Status Summary:`);
    console.log(`   🟢 Available: ${available} promotions`);
    console.log(`   🟡 Used up: ${used} promotions`);
    console.log(`   🔴 Expired: ${expired} promotions`);
    
    // Demo 4: Usage Tracking
    console.log("\n📋 DEMO 4: Usage Tracking Demo");
    console.log("-" .repeat(40));
    
    // Simulate using a promotion
    const testPromotion = claimedPromotions.find(pu => 
      pu.promotionId && pu.usedCount < (pu.promotionId.maxUsagePerUser || 1)
    );
    
    if (testPromotion) {
      const beforeUsage = testPromotion.usedCount;
      await testPromotion.incrementUsage(new mongoose.Types.ObjectId());
      
      console.log(`🎯 Simulated using promotion: ${testPromotion.promotionId.code}`);
      console.log(`   Usage count: ${beforeUsage} → ${testPromotion.usedCount}`);
      console.log(`   Max usage: ${testPromotion.promotionId.maxUsagePerUser}`);
      console.log(`   Can still use: ${testPromotion.usedCount < testPromotion.promotionId.maxUsagePerUser ? 'Yes' : 'No'}`);
    }
    
    console.log("\n" + "=" .repeat(60));
    console.log("🎯 FEATURE SUMMARY:");
    console.log("✅ PUBLIC/PRIVATE promotion types");
    console.log("✅ Claim promotions by code");
    console.log("✅ User promotion collection");
    console.log("✅ Usage tracking and limits");
    console.log("✅ Status management (available/used/expired)");
    console.log("✅ Duplicate claim prevention");
    console.log("✅ Real-time usage updates");
    console.log("\n🚀 Ready for production use!");
    
  } catch (error) {
    console.error("❌ Demo failed:", error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Chạy demo
demoCompletePromotionFeatures();
