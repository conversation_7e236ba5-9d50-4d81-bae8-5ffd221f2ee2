require('dotenv').config();
const axios = require('axios');

// Test improved sorting: Available first, no expired
async function testImprovedSorting() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  // Helper function to get promotion status
  const getPromotionStatus = (promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);
    
    if (now < startDate) {
      return { status: 'upcoming', message: 'Starting Soon' };
    } else if (now > endDate) {
      return { status: 'expired', message: 'Expired' };
    } else if (promotion.userCanUse === false) {
      return { status: 'used up', message: 'Used Up' };
    } else {
      return { status: 'active', message: 'Available' };
    }
  };
  
  try {
    console.log("🎯 Testing Improved Sorting: Available First, No Expired");
    console.log("=" .repeat(70));
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      const allPromotions = response.data.promotions;
      
      console.log(`📊 Total promotions from API: ${allPromotions.length}`);
      
      // Apply NEW filtering logic (filter out expired)
      const filteredPromotions = allPromotions.filter(promotion => {
        // Filter out expired promotions
        const now = new Date();
        const endDate = new Date(promotion.endDate);
        if (now > endDate) {
          return false; // Don't show expired promotions
        }
        
        // PUBLIC promotions are available to all users
        if (promotion.type === 'PUBLIC') {
          return true;
        }
        // PRIVATE promotions must be claimed
        return promotion.isClaimed === true;
      });
      
      console.log(`📊 After filtering (no expired): ${filteredPromotions.length}`);
      
      // Analyze status distribution
      const statusCounts = { active: 0, upcoming: 0, 'used up': 0, expired: 0 };
      
      filteredPromotions.forEach(promo => {
        const status = getPromotionStatus(promo).status;
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      });
      
      console.log(`\n📊 Status Distribution (after filtering):`);
      console.log(`✅ Available (active): ${statusCounts.active} promotions`);
      console.log(`⏳ Starting Soon (upcoming): ${statusCounts.upcoming} promotions`);
      console.log(`🚫 Used Up: ${statusCounts['used up']} promotions`);
      console.log(`❌ Expired: ${statusCounts.expired} promotions (should be 0)`);
      
      // Apply NEW sorting logic
      console.log(`\n🎯 Testing NEW Sorting Logic:`);
      console.log(`Priority: Available → PUBLIC/PRIVATE → Starting Soon → Used Up`);
      
      const sortedPromotions = [...filteredPromotions].sort((a, b) => {
        // Priority 1: Available promotions first (regardless of type)
        const statusA = getPromotionStatus(a).status;
        const statusB = getPromotionStatus(b).status;
        
        const isAvailableA = statusA === 'active';
        const isAvailableB = statusB === 'active';
        
        if (isAvailableA && !isAvailableB) return -1;
        if (!isAvailableA && isAvailableB) return 1;
        
        // Priority 2: Within same availability, PUBLIC first, PRIVATE last
        if (a.type === 'PUBLIC' && b.type === 'PRIVATE') return -1;
        if (a.type === 'PRIVATE' && b.type === 'PUBLIC') return 1;
        
        // Priority 3: Within same type and availability, sort by status
        const statusPriority = { 'active': 1, 'upcoming': 2, 'used up': 3 };
        const priorityA = statusPriority[statusA] || 4;
        const priorityB = statusPriority[statusB] || 4;
        
        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }
        
        // Priority 4: Within same status, sort by claimed date (newest first)
        return new Date(b.claimedAt || b.createdAt) - new Date(a.claimedAt || a.createdAt);
      });
      
      console.log(`\n📋 My Promotions Display Order:`);
      
      let currentSection = '';
      sortedPromotions.forEach((promo, i) => {
        const statusInfo = getPromotionStatus(promo);
        const isAvailable = statusInfo.status === 'active';
        const section = isAvailable ? 
          `AVAILABLE - ${promo.type}` : 
          `${statusInfo.status.toUpperCase()} - ${promo.type}`;
        
        if (section !== currentSection) {
          console.log(`\n--- ${section} ---`);
          currentSection = section;
        }
        
        const statusIcon = {
          'active': '✅',
          'upcoming': '⏳',
          'used up': '🚫',
          'expired': '❌'
        }[statusInfo.status] || '❓';
        
        const typeIcon = promo.type === 'PUBLIC' ? '🌐' : '🔒';
        
        console.log(`${i + 1}. ${statusIcon} ${typeIcon} ${promo.code} - ${promo.name}`);
        console.log(`   Status: ${statusInfo.message}`);
        console.log(`   Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
      });
      
      // Verify sorting correctness
      console.log(`\n📊 Sorting Verification:`);
      
      // Group promotions by priority
      const availablePublic = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'active' && p.type === 'PUBLIC'
      );
      const availablePrivate = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'active' && p.type === 'PRIVATE'
      );
      const upcomingPublic = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'upcoming' && p.type === 'PUBLIC'
      );
      const upcomingPrivate = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'upcoming' && p.type === 'PRIVATE'
      );
      const usedUpPublic = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'used up' && p.type === 'PUBLIC'
      );
      const usedUpPrivate = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'used up' && p.type === 'PRIVATE'
      );
      
      console.log(`Expected Order:`);
      console.log(`1. ✅ Available PUBLIC: ${availablePublic.length} promotions`);
      console.log(`2. ✅ Available PRIVATE: ${availablePrivate.length} promotions`);
      console.log(`3. ⏳ Starting Soon PUBLIC: ${upcomingPublic.length} promotions`);
      console.log(`4. ⏳ Starting Soon PRIVATE: ${upcomingPrivate.length} promotions`);
      console.log(`5. 🚫 Used Up PUBLIC: ${usedUpPublic.length} promotions`);
      console.log(`6. 🚫 Used Up PRIVATE: ${usedUpPrivate.length} promotions`);
      
      // Check if available promotions are at the top
      const firstNonAvailable = sortedPromotions.findIndex(p => 
        getPromotionStatus(p).status !== 'active'
      );
      
      const totalAvailable = availablePublic.length + availablePrivate.length;
      
      if (firstNonAvailable === -1 || firstNonAvailable === totalAvailable) {
        console.log(`✅ SUCCESS: All available promotions are at the top!`);
      } else {
        console.log(`❌ ISSUE: Available promotions not properly grouped at top`);
      }
      
      // Check if expired promotions are filtered out
      const expiredCount = sortedPromotions.filter(p => 
        getPromotionStatus(p).status === 'expired'
      ).length;
      
      if (expiredCount === 0) {
        console.log(`✅ SUCCESS: No expired promotions in the list!`);
      } else {
        console.log(`❌ ISSUE: ${expiredCount} expired promotions still showing`);
      }
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(70));
    console.log("🎯 IMPROVED SORTING SUMMARY:");
    console.log("✅ New sorting priority:");
    console.log("   1️⃣ Available promotions first (can use now)");
    console.log("   2️⃣ Within available: PUBLIC before PRIVATE");
    console.log("   3️⃣ Starting Soon promotions (upcoming)");
    console.log("   4️⃣ Used Up promotions last");
    console.log("   ❌ Expired promotions filtered out completely");
    console.log("✅ Benefits:");
    console.log("   🎯 Most useful promotions at the top");
    console.log("   🧹 Clean list without expired clutter");
    console.log("   📈 Better user experience");
    console.log("   ⚡ Quick access to usable promotions");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testImprovedSorting();
