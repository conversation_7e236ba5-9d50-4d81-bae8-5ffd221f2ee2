require('dotenv').config();
const mongoose = require('mongoose');
const PromotionUser = require('./src/models/PromotionUser');
const Promotion = require('./src/models/promotion');
const User = require('./src/models/user');

// Debug My Promotions page - tại sao không hiển thị promotion nào
async function debugMyPromotions() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
    
    console.log("🔍 DEBUGGING MY PROMOTIONS PAGE");
    console.log("=" .repeat(50));
    
    // Test với user ID 11
    const userId = 11;
    
    // Step 1: Kiểm tra user có tồn tại không
    console.log("\n📋 Step 1: Check User Existence");
    const user = await User.findOne({ _id: userId });
    if (user) {
      console.log(`✅ User found: ${user.name} (${user.email})`);
    } else {
      console.log(`❌ User with ID ${userId} not found`);
      return;
    }
    
    // Step 2: <PERSON><PERSON><PERSON> tra tất cả promotions trong database
    console.log("\n📋 Step 2: Check All Promotions in Database");
    const allPromotions = await Promotion.find({});
    console.log(`📊 Total promotions in database: ${allPromotions.length}`);
    
    allPromotions.forEach((promo, index) => {
      console.log(`${index + 1}. ${promo.code} - ${promo.name} (${promo.type}, ${promo.isActive ? 'Active' : 'Inactive'})`);
    });
    
    // Step 3: Kiểm tra PromotionUser records
    console.log("\n📋 Step 3: Check PromotionUser Records");
    const allPromotionUsers = await PromotionUser.find({});
    console.log(`📊 Total PromotionUser records: ${allPromotionUsers.length}`);
    
    const userPromotionUsers = await PromotionUser.find({ userId: userId });
    console.log(`📊 PromotionUser records for user ${userId}: ${userPromotionUsers.length}`);
    
    if (userPromotionUsers.length > 0) {
      console.log(`📝 User's PromotionUser records:`);
      userPromotionUsers.forEach((pu, index) => {
        console.log(`${index + 1}. Promotion: ${pu.promotionId}, Claimed: ${pu.isClaimed}, Used: ${pu.usedCount}`);
      });
    } else {
      console.log(`❌ No PromotionUser records found for user ${userId}`);
      console.log(`💡 This means user hasn't claimed any promotions yet`);
    }
    
    // Step 4: Kiểm tra claimed promotions
    console.log("\n📋 Step 4: Check Claimed Promotions");
    const claimedPromotions = await PromotionUser.find({ 
      userId: userId,
      isClaimed: true 
    }).populate('promotionId');
    
    console.log(`📊 Claimed promotions for user ${userId}: ${claimedPromotions.length}`);
    
    if (claimedPromotions.length > 0) {
      console.log(`📝 Claimed promotions details:`);
      claimedPromotions.forEach((pu, index) => {
        if (pu.promotionId) {
          console.log(`${index + 1}. ${pu.promotionId.code} - ${pu.promotionId.name}`);
          console.log(`   Type: ${pu.promotionId.type}, Active: ${pu.promotionId.isActive}`);
          console.log(`   Claimed: ${pu.claimedAt}, Used: ${pu.usedCount}/${pu.promotionId.maxUsagePerUser}`);
        } else {
          console.log(`${index + 1}. ❌ Promotion not found (deleted?)`);
        }
      });
    } else {
      console.log(`❌ No claimed promotions found for user ${userId}`);
    }
    
    // Step 5: Simulate API response
    console.log("\n📋 Step 5: Simulate API Response");
    
    const validClaimedPromotions = claimedPromotions
      .filter(pu => pu.promotionId && pu.promotionId.isActive)
      .map(pu => {
        const promotion = pu.promotionId;
        const now = new Date();
        const isValid = now >= promotion.startDate && now <= promotion.endDate;
        const canUse = pu.usedCount < (promotion.maxUsagePerUser || 1);
        
        return {
          _id: promotion._id,
          code: promotion.code,
          name: promotion.name,
          type: promotion.type,
          status: !isValid ? 'expired' : !canUse ? 'used_up' : 'available',
          userUsedCount: pu.usedCount,
          maxUsagePerUser: promotion.maxUsagePerUser,
          userCanUse: canUse && isValid,
          claimedAt: pu.claimedAt
        };
      });
    
    console.log(`📊 API would return: ${validClaimedPromotions.length} promotions`);
    
    if (validClaimedPromotions.length > 0) {
      validClaimedPromotions.forEach((promo, index) => {
        console.log(`${index + 1}. ${promo.code} - ${promo.status} (${promo.userUsedCount}/${promo.maxUsagePerUser})`);
      });
    } else {
      console.log(`❌ API would return empty array`);
    }
    
    // Step 6: Suggest solutions
    console.log("\n📋 Step 6: Suggested Solutions");
    
    if (userPromotionUsers.length === 0) {
      console.log(`💡 SOLUTION 1: User needs to claim some promotions first`);
      console.log(`   - Go to PromotionModal and enter promotion codes`);
      console.log(`   - Try codes: SECRET20, INSIDER15, HIDDEN50 (private promotions)`);
      console.log(`   - Or apply public promotions to auto-claim them`);
      
      // Auto-claim some promotions for testing
      console.log(`\n🔧 Auto-claiming some promotions for testing...`);
      
      const testPromotions = await Promotion.find({ 
        code: { $in: ['SECRET20', 'WELCOME50K', 'VIP30'] },
        isActive: true 
      });
      
      for (const promo of testPromotions) {
        const existingPU = await PromotionUser.findOne({
          promotionId: promo._id,
          userId: userId
        });
        
        if (!existingPU) {
          const newPU = new PromotionUser({
            promotionId: promo._id,
            userId: userId
          });
          await newPU.claimPromotion();
          console.log(`✅ Auto-claimed: ${promo.code}`);
        } else {
          console.log(`ℹ️ Already exists: ${promo.code}`);
        }
      }
      
      // Re-check after auto-claiming
      const newClaimedPromotions = await PromotionUser.find({ 
        userId: userId,
        isClaimed: true 
      }).populate('promotionId');
      
      console.log(`📊 After auto-claiming: ${newClaimedPromotions.length} claimed promotions`);
      
    } else if (claimedPromotions.length === 0) {
      console.log(`💡 SOLUTION 2: User has PromotionUser records but none are claimed`);
      console.log(`   - Check isClaimed field in PromotionUser records`);
      console.log(`   - Use claimPromotion() method to claim them`);
      
    } else if (validClaimedPromotions.length === 0) {
      console.log(`💡 SOLUTION 3: User has claimed promotions but they're inactive/expired`);
      console.log(`   - Check promotion isActive status`);
      console.log(`   - Check promotion start/end dates`);
      console.log(`   - Claim some active promotions`);
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 DEBUG SUMMARY:");
    console.log(`📊 Total promotions: ${allPromotions.length}`);
    console.log(`📊 User's PromotionUser records: ${userPromotionUsers.length}`);
    console.log(`📊 User's claimed promotions: ${claimedPromotions.length}`);
    console.log(`📊 Valid claimed promotions: ${validClaimedPromotions.length}`);
    
    if (validClaimedPromotions.length === 0) {
      console.log(`❌ ISSUE: My Promotions page will show empty`);
      console.log(`💡 SOLUTION: User needs to claim promotions first`);
    } else {
      console.log(`✅ SUCCESS: My Promotions page should show ${validClaimedPromotions.length} promotions`);
    }
    
  } catch (error) {
    console.error("❌ Debug failed:", error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Chạy debug
debugMyPromotions();
