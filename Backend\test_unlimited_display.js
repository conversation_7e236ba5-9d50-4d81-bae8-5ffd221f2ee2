require('dotenv').config();
const axios = require('axios');

// Test unlimited promotion display
async function testUnlimitedDisplay() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  try {
    console.log("🎯 Testing Unlimited Promotion Display");
    console.log("=" .repeat(60));
    
    const response = await axios.get(`${baseURL}/promotions`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ API Response Status: ${response.status}`);
    console.log(`📊 Total promotions: ${response.data.promotions?.length || 0}`);
    
    if (response.data.promotions && response.data.promotions.length > 0) {
      const promotions = response.data.promotions;
      
      // Separate limited and unlimited promotions
      const limitedPromotions = promotions.filter(p => p.usageLimit);
      const unlimitedPromotions = promotions.filter(p => !p.usageLimit);
      
      console.log(`\n📊 Promotion Categories:`);
      console.log(`   📈 Limited promotions: ${limitedPromotions.length}`);
      console.log(`   ♾️ Unlimited promotions: ${unlimitedPromotions.length}`);
      
      // Test unlimited promotions display
      console.log(`\n📋 Unlimited Promotions Display Test`);
      console.log("-" .repeat(40));
      
      if (unlimitedPromotions.length > 0) {
        unlimitedPromotions.forEach((promo, index) => {
          console.log(`\n${index + 1}. ${promo.code} - ${promo.name}`);
          console.log(`   Type: ${promo.type}`);
          console.log(`   Usage Limit: ${promo.usageLimit || 'None (Unlimited)'}`);
          console.log(`   Used Count: ${promo.usedCount || 0}`);
          console.log(`   Claimed: ${promo.isClaimed ? 'Yes' : 'No'}`);
          
          // Frontend display simulation
          console.log(`\n   🎯 PromotionModal Display:`);
          console.log(`      Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
          console.log(`      🌍 Global Usage: Unlimited`);
          console.log(`      Status: ✅ Available`);
          
          console.log(`\n   🎯 My Promotions Display:`);
          console.log(`      Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
          console.log(`      Global Usage: Unlimited`);
          if (promo.claimedAt) {
            console.log(`      Claimed: ${new Date(promo.claimedAt).toLocaleDateString()}`);
          }
        });
      } else {
        console.log(`   ❌ No unlimited promotions found`);
      }
      
      // Test limited promotions for comparison
      console.log(`\n📋 Limited Promotions Display Test (for comparison)`);
      console.log("-" .repeat(40));
      
      if (limitedPromotions.length > 0) {
        limitedPromotions.slice(0, 3).forEach((promo, index) => {
          const percentage = Math.round(((promo.usedCount || 0) / promo.usageLimit) * 100);
          
          console.log(`\n${index + 1}. ${promo.code} - ${promo.name}`);
          console.log(`   Usage Limit: ${promo.usageLimit}`);
          console.log(`   Used Count: ${promo.usedCount || 0}`);
          
          console.log(`\n   🎯 PromotionModal Display:`);
          console.log(`      Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
          console.log(`      🌍 Global Usage: ${percentage}%`);
          console.log(`      Progress Bar: [████████████████████████████████]`);
          
          console.log(`\n   🎯 My Promotions Display:`);
          console.log(`      Your Usage: ${promo.userUsedCount || 0}/${promo.maxUsagePerUser || 1}`);
          console.log(`      Global Usage: ${promo.usedCount || 0}/${promo.usageLimit}`);
        });
      }
      
      // Test UI layout comparison
      console.log(`\n📋 UI Layout Comparison`);
      console.log("-" .repeat(40));
      
      console.log(`\n🎯 PromotionModal Cards:`);
      
      // Unlimited promotion card
      if (unlimitedPromotions.length > 0) {
        const unlimitedPromo = unlimitedPromotions[0];
        console.log(`\n♾️ Unlimited Promotion Card:`);
        console.log(`   ┌─────────────────────────────────────┐`);
        console.log(`   │ ${unlimitedPromo.code.padEnd(35)} │`);
        console.log(`   │ Your Usage: ${(unlimitedPromo.userUsedCount || 0)}/${unlimitedPromo.maxUsagePerUser || 1}${' '.repeat(20)} │`);
        console.log(`   │ 🌍 Global Usage: Unlimited${' '.repeat(10)} │`);
        console.log(`   │ (No progress bar needed)${' '.repeat(11)} │`);
        console.log(`   │ ✅ Available${' '.repeat(22)} │`);
        console.log(`   └─────────────────────────────────────┘`);
      }
      
      // Limited promotion card
      if (limitedPromotions.length > 0) {
        const limitedPromo = limitedPromotions[0];
        const percentage = Math.round(((limitedPromo.usedCount || 0) / limitedPromo.usageLimit) * 100);
        console.log(`\n📈 Limited Promotion Card:`);
        console.log(`   ┌─────────────────────────────────────┐`);
        console.log(`   │ ${limitedPromo.code.padEnd(35)} │`);
        console.log(`   │ Your Usage: ${(limitedPromo.userUsedCount || 0)}/${limitedPromo.maxUsagePerUser || 1}${' '.repeat(20)} │`);
        console.log(`   │ 🌍 Global Usage:${' '.repeat(15)}${percentage}% │`);
        console.log(`   │ [████████████████████████████████] │`);
        console.log(`   │ ✅ Available${' '.repeat(22)} │`);
        console.log(`   └─────────────────────────────────────┘`);
      }
      
      // Test edge cases
      console.log(`\n📋 Edge Cases Test`);
      console.log("-" .repeat(40));
      
      const edgeCases = [
        { 
          name: "Unlimited with 0 uses", 
          usageLimit: null, 
          usedCount: 0,
          display: "Unlimited"
        },
        { 
          name: "Unlimited with many uses", 
          usageLimit: null, 
          usedCount: 1000,
          display: "Unlimited"
        },
        { 
          name: "Limited with 0 uses", 
          usageLimit: 100, 
          usedCount: 0,
          display: "0/100"
        },
        { 
          name: "Limited exhausted", 
          usageLimit: 50, 
          usedCount: 50,
          display: "50/50"
        }
      ];
      
      console.log(`Frontend Display Logic Test:`);
      edgeCases.forEach((testCase, i) => {
        console.log(`${i + 1}. ${testCase.name}:`);
        console.log(`   Data: usageLimit=${testCase.usageLimit}, usedCount=${testCase.usedCount}`);
        console.log(`   Display: "${testCase.display}"`);
        
        // Test the actual logic
        const actualDisplay = testCase.usageLimit ? 
          `${testCase.usedCount}/${testCase.usageLimit}` : 
          'Unlimited';
        
        if (actualDisplay === testCase.display) {
          console.log(`   ✅ Correct`);
        } else {
          console.log(`   ❌ Expected "${testCase.display}", got "${actualDisplay}"`);
        }
      });
      
    } else {
      console.log(`❌ No promotions returned from API`);
    }
    
    console.log("\n" + "=" .repeat(60));
    console.log("🎯 UNLIMITED DISPLAY SUMMARY:");
    console.log("✅ Clean unlimited promotion display");
    console.log("✅ PromotionModal shows:");
    console.log("   👤 Your Usage: X/Y (personal limit still applies)");
    console.log("   🌍 Global Usage: Unlimited (no progress bar)");
    console.log("✅ My Promotions shows:");
    console.log("   👤 Your Usage: X/Y");
    console.log("   🌍 Global Usage: Unlimited");
    console.log("✅ No confusing usage counts for unlimited promotions");
    console.log("✅ Clear distinction between limited and unlimited");
    console.log("✅ Simplified UI - no unnecessary progress bars");
    console.log("✅ Better user understanding of promotion types");
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log(`❌ Authentication failed: Token may be expired`);
    } else {
      console.log(`❌ API Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Chạy test
testUnlimitedDisplay();
