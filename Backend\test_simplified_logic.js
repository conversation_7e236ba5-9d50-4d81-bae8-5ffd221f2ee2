require('dotenv').config();
const mongoose = require('mongoose');
const PromotionUser = require('./src/models/PromotionUser');
const Promotion = require('./src/models/promotion');

// Test logic đơn gi<PERSON>n hóa
async function testSimplifiedLogic() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
    
    console.log("🎯 Testing Simplified Promotion Logic");
    console.log("=" .repeat(50));
    
    const userId = 11; // Test user ID
    
    // Test 1: Simulate getAllPromotions logic
    console.log("\n📋 Test 1: Simulate getAllPromotions API");
    
    // Get public promotions
    const publicPromotions = await Promotion.find({ 
      type: 'PUBLIC',
      isActive: true 
    }).sort({ createdAt: -1 });
    
    console.log(`🌐 Public promotions: ${publicPromotions.length}`);
    
    // Get user's claimed private promotions
    const claimedPromotions = await PromotionUser.find({ 
      userId: userId,
      isClaimed: true 
    }).populate('promotionId');
    
    const claimedPrivatePromotions = claimedPromotions
      .filter(pu => pu.promotionId && 
                   pu.promotionId.isActive && 
                   pu.promotionId.type === 'PRIVATE' &&
                   !publicPromotions.some(p => p._id.toString() === pu.promotionId._id.toString()))
      .map(pu => pu.promotionId);
    
    console.log(`🔒 Claimed private promotions: ${claimedPrivatePromotions.length}`);
    
    // Combine all promotions
    const allPromotions = [...publicPromotions, ...claimedPrivatePromotions];
    console.log(`📊 Total promotions for user: ${allPromotions.length}`);
    
    // Get user usage for all promotions
    const promotionIds = allPromotions.map(p => p._id);
    const userUsages = await PromotionUser.find({
      promotionId: { $in: promotionIds },
      userId: userId
    });
    
    // Create usage map
    const usageMap = {};
    userUsages.forEach(usage => {
      usageMap[usage.promotionId.toString()] = {
        usedCount: usage.usedCount,
        isClaimed: usage.isClaimed
      };
    });
    
    // Add usage info to promotions
    const promotionsWithUsage = allPromotions.map(promotion => {
      const userUsage = usageMap[promotion._id.toString()];
      return {
        code: promotion.code,
        name: promotion.name,
        type: promotion.type,
        userUsedCount: userUsage?.usedCount || 0,
        userCanUse: (userUsage?.usedCount || 0) < (promotion.maxUsagePerUser || 1),
        isClaimed: userUsage?.isClaimed || false
      };
    });
    
    console.log(`\n📝 Promotions with usage info:`);
    promotionsWithUsage.forEach((p, i) => {
      console.log(`${i + 1}. ${p.code} (${p.type})`);
      console.log(`   Claimed: ${p.isClaimed}, Usage: ${p.userUsedCount}/${p.maxUsagePerUser || 1}, Can use: ${p.userCanUse}`);
    });
    
    // Test 2: Simulate auto-claim when applying
    console.log("\n📋 Test 2: Simulate auto-claim when applying");
    
    const testCode = 'HIDDEN50';
    const promotion = await Promotion.findOne({ code: testCode, isActive: true });
    
    if (promotion) {
      console.log(`🎯 Testing promotion: ${promotion.code} (${promotion.type})`);
      
      let promotionUser = await PromotionUser.findOne({
        promotionId: promotion._id,
        userId: userId
      });
      
      console.log(`📊 Before apply:`);
      console.log(`   - PromotionUser exists: ${!!promotionUser}`);
      console.log(`   - Is claimed: ${promotionUser?.isClaimed || false}`);
      console.log(`   - Used count: ${promotionUser?.usedCount || 0}`);
      
      // Simulate auto-claim logic
      if (!promotionUser) {
        promotionUser = new PromotionUser({
          promotionId: promotion._id,
          userId: userId
        });
        console.log(`✅ Created new PromotionUser record`);
      }
      
      if (!promotionUser.isClaimed) {
        await promotionUser.claimPromotion();
        console.log(`✅ Auto-claimed promotion`);
      }
      
      console.log(`📊 After auto-claim:`);
      console.log(`   - Is claimed: ${promotionUser.isClaimed}`);
      console.log(`   - Claimed at: ${promotionUser.claimedAt?.toLocaleString()}`);
      
      // Simulate usage increment (when order is completed)
      await promotionUser.incrementUsage(new mongoose.Types.ObjectId());
      console.log(`✅ Incremented usage count`);
      
      console.log(`📊 After usage:`);
      console.log(`   - Used count: ${promotionUser.usedCount}`);
      console.log(`   - Can still use: ${promotionUser.usedCount < (promotion.maxUsagePerUser || 1)}`);
      
    } else {
      console.log(`❌ Promotion ${testCode} not found`);
    }
    
    // Test 3: Verify promotion now appears in list
    console.log("\n📋 Test 3: Verify promotion appears in unified list");
    
    // Re-run the getAllPromotions logic
    const updatedPublicPromotions = await Promotion.find({ 
      type: 'PUBLIC',
      isActive: true 
    });
    
    const updatedClaimedPromotions = await PromotionUser.find({ 
      userId: userId,
      isClaimed: true 
    }).populate('promotionId');
    
    const updatedClaimedPrivatePromotions = updatedClaimedPromotions
      .filter(pu => pu.promotionId && 
                   pu.promotionId.isActive && 
                   pu.promotionId.type === 'PRIVATE' &&
                   !updatedPublicPromotions.some(p => p._id.toString() === pu.promotionId._id.toString()))
      .map(pu => pu.promotionId);
    
    const updatedAllPromotions = [...updatedPublicPromotions, ...updatedClaimedPrivatePromotions];
    
    console.log(`📊 Updated promotion counts:`);
    console.log(`   - Public: ${updatedPublicPromotions.length}`);
    console.log(`   - Claimed private: ${updatedClaimedPrivatePromotions.length}`);
    console.log(`   - Total: ${updatedAllPromotions.length}`);
    
    const hiddenPromo = updatedAllPromotions.find(p => p.code === 'HIDDEN50');
    if (hiddenPromo) {
      console.log(`✅ HIDDEN50 now appears in unified promotion list`);
    } else {
      console.log(`❌ HIDDEN50 still not in unified promotion list`);
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 Simplified Logic Summary:");
    console.log("✅ Public promotions always visible");
    console.log("✅ Private promotions appear after claiming");
    console.log("✅ Auto-claim works when applying promotions");
    console.log("✅ Usage tracking integrated seamlessly");
    console.log("✅ Single unified promotion list for frontend");
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Chạy test
testSimplifiedLogic();
