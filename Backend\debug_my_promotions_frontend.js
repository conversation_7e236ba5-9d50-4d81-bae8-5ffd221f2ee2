require('dotenv').config();
const axios = require('axios');

// Debug My Promotions frontend issue
async function debugMyPromotionsFrontend() {
  const baseURL = "http://localhost:5000/api";
  const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7Il9pZCI6MTEsIm5hbWUiOiJOZ3V54buFbiBWxINuIEEiLCJlbWFpbCI6ImN1czFAZ20uY29tIiwicm9sZSI6IkNVU1RPTUVSIiwic3RhdHVzIjoiQUNUSVZFIiwiaXNWZXJpZmllZCI6dHJ1ZSwiaXNMb2NrZWQiOmZhbHNlfSwiaWF0IjoxNzUzMDk3NTY3LCJleHAiOjE3ODQ2MzM1NjcsImlzcyI6Imlzc3VlciJ9.YUKGIGr4nZYOorRpRgxGqH49a8fYU4f_WghVXsrW4yo";
  
  try {
    console.log("🔍 DEBUGGING MY PROMOTIONS FRONTEND ISSUE");
    console.log("=" .repeat(60));
    
    // Test 1: Compare PromotionModal API vs My Promotions API
    console.log("\n📋 Test 1: Compare API Responses");
    console.log("-" .repeat(40));
    
    // PromotionModal API (works)
    console.log("🎯 Testing PromotionModal API: GET /api/promotions");
    try {
      const modalResponse = await axios.get(`${baseURL}/promotions`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`✅ PromotionModal API: ${modalResponse.status}`);
      console.log(`📊 Total promotions: ${modalResponse.data.promotions?.length || 0}`);
      
      if (modalResponse.data.promotions) {
        const claimedInModal = modalResponse.data.promotions.filter(p => p.isClaimed);
        console.log(`📊 Claimed promotions in modal: ${claimedInModal.length}`);
        claimedInModal.forEach(p => {
          console.log(`   - ${p.code} (${p.type}): claimed=${p.isClaimed}, usage=${p.userUsedCount}/${p.maxUsagePerUser}`);
        });
      }
      
    } catch (error) {
      console.log(`❌ PromotionModal API failed: ${error.response?.data?.message || error.message}`);
    }
    
    // My Promotions API (not working in frontend)
    console.log("\n🎯 Testing My Promotions API: GET /api/promotions/claimed");
    try {
      const myPromotionsResponse = await axios.get(`${baseURL}/promotions/claimed`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`✅ My Promotions API: ${myPromotionsResponse.status}`);
      console.log(`📊 Total claimed promotions: ${myPromotionsResponse.data.promotions?.length || 0}`);
      console.log(`📊 Stats:`, myPromotionsResponse.data.stats);
      
      if (myPromotionsResponse.data.promotions) {
        myPromotionsResponse.data.promotions.forEach((p, i) => {
          console.log(`   ${i + 1}. ${p.code} (${p.type}): status=${p.status}, usage=${p.userUsedCount}/${p.maxUsagePerUser}`);
        });
      }
      
    } catch (error) {
      console.log(`❌ My Promotions API failed: ${error.response?.data?.message || error.message}`);
    }
    
    // Test 2: Check API response structure differences
    console.log("\n📋 Test 2: API Response Structure Analysis");
    console.log("-" .repeat(40));
    
    try {
      const [modalResp, myPromotionsResp] = await Promise.all([
        axios.get(`${baseURL}/promotions`, { headers: { Authorization: `Bearer ${token}` } }),
        axios.get(`${baseURL}/promotions/claimed`, { headers: { Authorization: `Bearer ${token}` } })
      ]);
      
      console.log("🔍 PromotionModal Response Structure:");
      console.log(`   - Root keys: ${Object.keys(modalResp.data)}`);
      console.log(`   - promotions type: ${Array.isArray(modalResp.data.promotions) ? 'Array' : typeof modalResp.data.promotions}`);
      
      console.log("\n🔍 My Promotions Response Structure:");
      console.log(`   - Root keys: ${Object.keys(myPromotionsResp.data)}`);
      console.log(`   - promotions type: ${Array.isArray(myPromotionsResp.data.promotions) ? 'Array' : typeof myPromotionsResp.data.promotions}`);
      
      // Check if structures match what Redux saga expects
      console.log("\n🔍 Redux Saga Compatibility Check:");
      
      // Check PromotionModal structure
      const modalHasPromotions = modalResp.data.promotions && Array.isArray(modalResp.data.promotions);
      console.log(`   ✅ PromotionModal has promotions array: ${modalHasPromotions}`);
      
      // Check My Promotions structure
      const myPromotionsHasPromotions = myPromotionsResp.data.promotions && Array.isArray(myPromotionsResp.data.promotions);
      console.log(`   ${myPromotionsHasPromotions ? '✅' : '❌'} My Promotions has promotions array: ${myPromotionsHasPromotions}`);
      
      if (myPromotionsHasPromotions) {
        console.log(`   📊 My Promotions array length: ${myPromotionsResp.data.promotions.length}`);
        
        if (myPromotionsResp.data.promotions.length > 0) {
          const sample = myPromotionsResp.data.promotions[0];
          console.log(`   📝 Sample promotion keys: ${Object.keys(sample)}`);
          console.log(`   📝 Has required fields: code=${!!sample.code}, name=${!!sample.name}, type=${!!sample.type}`);
        }
      }
      
    } catch (error) {
      console.log(`❌ Structure analysis failed: ${error.message}`);
    }
    
    // Test 3: Frontend Debug Instructions
    console.log("\n📋 Test 3: Frontend Debug Instructions");
    console.log("-" .repeat(40));
    
    console.log("🔧 Steps to debug My Promotions frontend:");
    console.log("1. Open browser DevTools (F12)");
    console.log("2. Go to Console tab");
    console.log("3. Set token in localStorage:");
    console.log(`   localStorage.setItem('token', '${token.substring(0, 50)}...');`);
    console.log("4. Navigate to My Account > My Promotions");
    console.log("5. Look for these console logs:");
    console.log("   - '🎯 Component: Dispatching FETCH_USER_PROMOTIONS action'");
    console.log("   - '🚀 Redux Saga: Fetching promotions from API...'");
    console.log("   - '✅ Redux Saga: API Response:'");
    console.log("   - '🔍 Reducer: FETCH_USER_PROMOTIONS_SUCCESS payload:'");
    console.log("6. Check Network tab for API calls to /api/promotions/claimed");
    console.log("7. Check Redux DevTools for state changes");
    
    console.log("\n🔍 Common Issues to Check:");
    console.log("❓ Is user authenticated? (check localStorage.getItem('token'))");
    console.log("❓ Is Redux saga running? (check console logs)");
    console.log("❓ Is API call successful? (check Network tab)");
    console.log("❓ Is response data correct? (check Redux DevTools)");
    console.log("❓ Is component rendering data? (check React DevTools)");
    
    // Test 4: Generate test data if needed
    console.log("\n📋 Test 4: Verify Test Data");
    console.log("-" .repeat(40));
    
    try {
      const response = await axios.get(`${baseURL}/promotions/claimed`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.promotions && response.data.promotions.length > 0) {
        console.log(`✅ Test data exists: ${response.data.promotions.length} claimed promotions`);
        console.log("📊 Breakdown:");
        console.log(`   - Available: ${response.data.stats.available}`);
        console.log(`   - Used up: ${response.data.stats.usedUp}`);
        console.log(`   - Expired: ${response.data.stats.expired}`);
        console.log(`   - Public: ${response.data.stats.public}`);
        console.log(`   - Private: ${response.data.stats.private}`);
      } else {
        console.log("❌ No test data found - user has no claimed promotions");
        console.log("💡 Run debug_my_promotions.js to auto-claim some promotions");
      }
      
    } catch (error) {
      console.log(`❌ Test data check failed: ${error.message}`);
    }
    
    console.log("\n" + "=" .repeat(60));
    console.log("🎯 SUMMARY:");
    console.log("✅ Backend API /api/promotions/claimed works correctly");
    console.log("✅ Returns proper data structure with promotions array");
    console.log("✅ Authentication works with provided token");
    console.log("❓ Issue is likely in frontend Redux flow or component rendering");
    console.log("💡 Follow frontend debug instructions above to identify the issue");
    
  } catch (error) {
    console.error("❌ Debug failed:", error.message);
  }
}

// Chạy debug
debugMyPromotionsFrontend();
