require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/user');
const bcrypt = require('bcryptjs');

async function checkUserPassword() {
  await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
  
  const user = await User.findOne({ email: '<EMAIL>' }).select('+password');
  console.log('User found:', !!user);
  
  if (user) {
    console.log('User ID:', user._id);
    console.log('Email:', user.email);
    console.log('Role:', user.role);
    console.log('Has password:', !!user.password);
    
    // Test common passwords
    const testPasswords = ['123456', 'password', 'admin123', '123', '<EMAIL>'];
    for (const pwd of testPasswords) {
      const isMatch = await bcrypt.compare(pwd, user.password);
      console.log(`Password '${pwd}': ${isMatch ? '✅ Match' : '❌ No match'}`);
    }
  }
  
  await mongoose.disconnect();
}

checkUserPassword();
