require('dotenv').config();

// Test tính năng claim promotions bằng cách tạo data trực tiếp
async function testClaimPromotions() {
  const mongoose = require('mongoose');
  const PromotionUser = require('./src/models/PromotionUser');
  const Promotion = require('./src/models/promotion');

  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');

    console.log("🎁 Testing Claim Promotions Feature");
    console.log("=" .repeat(50));

    const userId = 11; // Test user ID

    // Test 1: Simulate claiming private promotions
    console.log("\n📋 Test 1: Simulate claiming PRIVATE promotions");

    const privatePromotions = ['SECRET20', 'INSIDER15', 'HIDDEN50'];

    for (const code of privatePromotions) {
      try {
        const promotion = await Promotion.findOne({ code: code });
        if (!promotion) {
          console.log(`❌ ${code}: Promotion not found`);
          continue;
        }

        // Check if already claimed
        let promotionUser = await PromotionUser.findOne({
          promotionId: promotion._id,
          userId: userId
        });

        if (promotionUser && promotionUser.isClaimed) {
          console.log(`⚠️ ${code}: Already claimed at ${promotionUser.claimedAt.toLocaleString()}`);
          continue;
        }

        // Create or update claim
        if (!promotionUser) {
          promotionUser = new PromotionUser({
            promotionId: promotion._id,
            userId: userId
          });
        }

        await promotionUser.claimPromotion();

        console.log(`✅ ${code}: Claimed successfully`);
        console.log(`   - Name: ${promotion.name}`);
        console.log(`   - Type: ${promotion.type}`);
        console.log(`   - Claimed at: ${promotionUser.claimedAt.toLocaleString()}`);

      } catch (error) {
        console.log(`❌ ${code}: ${error.message}`);
      }
    }

    // Test 2: Get user's claimed promotions
    console.log("\n📋 Test 2: Get user's claimed promotions");

    try {
      const claimedPromotions = await PromotionUser.find({
        userId: userId,
        isClaimed: true
      }).populate('promotionId');

      console.log(`✅ Total claimed promotions: ${claimedPromotions.length}`);

      let available = 0, used = 0, expired = 0;

      console.log(`\n📝 Claimed promotions list:`);
      claimedPromotions.forEach((pu, index) => {
        if (!pu.promotionId) return;

        const promotion = pu.promotionId;
        const now = new Date();
        const isValid = now >= promotion.startDate && now <= promotion.endDate;
        const canUse = pu.usedCount < (promotion.maxUsagePerUser || 1);

        let status = 'available';
        if (!isValid) {
          status = 'expired';
          expired++;
        } else if (!canUse) {
          status = 'used_up';
          used++;
        } else {
          available++;
        }

        console.log(`${index + 1}. ${promotion.code} - ${promotion.name}`);
        console.log(`   Type: ${promotion.type}`);
        console.log(`   Status: ${status.toUpperCase()}`);
        console.log(`   Usage: ${pu.usedCount}/${promotion.maxUsagePerUser}`);
        console.log(`   Claimed: ${pu.claimedAt.toLocaleString()}`);
        console.log('');
      });

      console.log(`📊 Status breakdown:`);
      console.log(`   - Available: ${available}`);
      console.log(`   - Used up: ${used}`);
      console.log(`   - Expired: ${expired}`);

    } catch (error) {
      console.log(`❌ Failed to get claimed promotions: ${error.message}`);
    }

    console.log("\n" + "=" .repeat(50));
    console.log("🎯 Summary:");
    console.log("✅ Users can claim promotions by entering codes");
    console.log("✅ Claimed promotions are saved to user's collection");
    console.log("✅ Duplicate claims are prevented");
    console.log("✅ Database tracks claim status and timestamps");
    console.log("✅ Both PUBLIC and PRIVATE promotions can be claimed");
    console.log("✅ Status tracking: available, used_up, expired");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Chạy test
testClaimPromotions();
