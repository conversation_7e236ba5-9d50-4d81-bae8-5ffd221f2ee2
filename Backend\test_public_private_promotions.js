require('dotenv').config();
const axios = require('axios');

// Test tính năng public/private promotions
async function testPublicPrivatePromotions() {
  const baseURL = "http://localhost:5000/api";
  
  try {
    console.log("🧪 Testing Public/Private Promotions Feature");
    console.log("=" .repeat(50));
    
    // Test 1: Kiểm tra API chỉ trả về public promotions
    console.log("\n📋 Test 1: Check API returns only PUBLIC promotions");
    const publicResponse = await axios.get(`${baseURL}/promotions`);
    
    console.log(`✅ Total promotions returned: ${publicResponse.data.promotions.length}`);
    console.log(`📊 Promotion types:`);
    
    const typeCount = {};
    publicResponse.data.promotions.forEach(p => {
      typeCount[p.type] = (typeCount[p.type] || 0) + 1;
    });
    
    Object.entries(typeCount).forEach(([type, count]) => {
      console.log(`   - ${type}: ${count} promotions`);
    });
    
    // Test 2: Thử apply private promotion bằng code
    console.log("\n📋 Test 2: Apply PRIVATE promotion by code");
    
    const privatePromotions = ['SECRET20', 'INSIDER15', 'HIDDEN50'];
    
    for (const code of privatePromotions) {
      try {
        const applyResponse = await axios.post(`${baseURL}/promotions/apply`, {
          code: code,
          orderAmount: 200
        }, {
          headers: {
            Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.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.McMsFmyTka1GumXgbH9cbjV5xIy0TvDJ4FIgBtxm6Bs`
          }
        });
        
        console.log(`✅ ${code}: Applied successfully - Discount: $${applyResponse.data.discount}`);
      } catch (error) {
        console.log(`❌ ${code}: Failed - ${error.response?.data?.message || error.message}`);
      }
    }
    
    // Test 3: Thử apply public promotion bằng code
    console.log("\n📋 Test 3: Apply PUBLIC promotion by code");
    
    try {
      const publicApplyResponse = await axios.post(`${baseURL}/promotions/apply`, {
        code: 'WELCOME50K',
        orderAmount: 150
      }, {
        headers: {
          Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.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.McMsFmyTka1GumXgbH9cbjV5xIy0TvDJ4FIgBtxm6Bs`
        }
      });
      
      console.log(`✅ WELCOME50K: Applied successfully - Discount: $${publicApplyResponse.data.discount}`);
    } catch (error) {
      console.log(`❌ WELCOME50K: Failed - ${error.response?.data?.message || error.message}`);
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 Summary:");
    console.log("✅ PUBLIC promotions: Visible in promotion list + can be applied by code");
    console.log("✅ PRIVATE promotions: Hidden from list but can be applied by code");
    console.log("✅ Manual code input in PromotionModal allows claiming private promotions");
    console.log("✅ Admin can set promotion type when creating/editing promotions");
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Chạy test
testPublicPrivatePromotions();
